# Swiss Geodata Accessibility Platform

Eine modulare Plattform zur Analyse und Visualisierung von Schweizer Geodaten mit **universeller Gemeinde-Filterung**. Das Projekt macht Schweizer Geodaten zugänglicher durch eine einheitliche Schnittstelle für verschiedene Datenquellen.

## Vision

Dieses Projekt zielt darauf ab, **Schweizer Geodaten zugänglicher zu machen**. Die allgemeine Zielsetzung ist es, Daten in `raw_input_data` zu speichern und alle verfügbaren Informationen für eine Gemeinde oder einen Ort auf der Karte abzurufen.

### Hauptanwendungsfälle
- **Universelle Gemeinde-Filterung**: Alle 2,140+ Schweizer Gemeinden mit datenquellenspezifischer Verfügbarkeitsanzeige
- **Gemeindebasierte Abfragen**: Alle verfügbaren Informationen für eine ausgewählte Gemeinde anzeigen
- **Kartenbasierte Abfragen**: Durch Klicken auf die Karte alle relevanten Geodaten für diesen Ort abrufen
- **ÖREB-Integration**: Echtzeitabfrage von Eigentumsbeschränkungen über offizielle kantonale ÖREB-Dienste
- **Modulare Datenquellen**: Einfache Integration neuer Geodatensätze aus der offiziellen Schweizer Geodateninfrastruktur

### Verfügbare Datenquellen
Das System unterstützt verschiedene Schweizer Geodatenquellen basierend auf der [offiziellen Geodateninfrastruktur](https://api.geobasisdaten.ch/api/v1/data/) mit **technischen IDs**:

- **73.1 Nutzungsplanung (kantonal/kommunal)** - Verfügbar ✅ (Technische ID: 820737)
- **39.3 SwissBoundaries3D** - Verfügbar ✅ (Technische ID: 821838)
- **Weitere 343 Datenquellen** - Geplant (siehe Datenquellen-Selektor in der Anwendung)

## Schnellstart

### 1. Prepare Data (Run Once)
```bash
# Prepare all spatial data for analysis
./raw_input_data/prepare_data.sh
```

### 2. Query Operations (Daily Use)
```bash
# Query zones for any municipality
./operations/query_municipality_zones.sh Steinhausen

# Discover available municipalities
./operations/discover_municipalities.sh search Stein

# Test that everything works
./operations/test_queries.sh
```

## Project Structure

```
├── raw_input_data/        # Raw data and preparation scripts
│   ├── prepare_data.sh           # Master preparation script
│   ├── 820737/                   # NPL Nutzungsplanung dataset
│   │   ├── convert_gpkg_to_geojson.sh    # Step 1: GPKG → GeoJSON
│   │   └── convert_geojson_to_parquet.sql # Step 2: GeoJSON → Parquet
│   └── 821838/                   # SwissBoundaries3D dataset
│       └── prepare_swissboundaries.sh     # SwissBoundaries3D setup
├── operations/            # Operational scripts (daily use)
│   ├── query_municipality_zones.sh   # Main query tool
│   ├── discover_municipalities.sh    # Find municipalities
│   ├── list_layers.sql              # Inspect data layers
│   └── test_queries.sh              # Verify operations
├── config/               # Configuration
│   └── data_sources.conf           # All settings and paths
├── data/                 # All data files
│   ├── input/                      # Raw input data
│   └── processed/                  # Converted data
│       ├── geojson/               # Intermediate GeoJSON
│       └── parquet/               # Final Parquet files
└── documentation/        # Additional docs
```

## Data Preparation (One-Time Setup)

The preparation phase converts raw GPKG files to efficient Parquet format using a two-step process that preserves spatial geometries:

1. **GPKG → GeoJSON**: Preserves curved geometries through linearization
2. **GeoJSON → Parquet**: Creates efficient spatial data for fast queries

```bash
# Run the master preparation script
./raw_input_data/prepare_data.sh
```

This script will:
- Set up the directory structure
- Convert all GPKG files to GeoJSON
- Convert GeoJSON files to Parquet
- Prepare SwissBoundaries3D data

## Daily Operations

Once data is prepared, use these operational scripts:

### Query Municipality Zones
```bash
# Query any Swiss municipality
./operations/query_municipality_zones.sh Steinhausen
./operations/query_municipality_zones.sh Zurich ZH
./operations/query_municipality_zones.sh Basel BS
```

### Discover Municipalities
```bash
# Search for municipalities
./operations/discover_municipalities.sh search Stein

# List municipalities by canton
./operations/discover_municipalities.sh canton ZG

# Show statistics
./operations/discover_municipalities.sh stats
```

### Inspect Data
```bash
# List available data layers
duckdb < operations/list_layers.sql

# Test spatial operations
./operations/test_queries.sh
```

## Configuration

All settings are centralized in `config/data_sources.conf`:

- **File paths**: Where to find input and processed data
- **Column names**: Database column mappings
- **Query settings**: Timeouts, result limits
- **Canton codes**: Swiss canton abbreviations

## Data Sources

### Geodienste (National Land Use Planning)
- **Source**: Federal Office for Spatial Development
- **Content**: Grundnutzung, Überlagernde Flächen/Linien/Punkte
- **Coverage**: 1,563 Swiss municipalities with spatial data

### ZH (Zurich Canton)
- **Source**: Canton Zurich Open Data
- **Content**: Nutzungsplanung, Abstandslinien
- **Coverage**: Zurich canton only

### SwissBoundaries3D
- **Source**: swisstopo (data.geo.admin.ch)
- **Content**: Municipality and canton boundaries
- **Purpose**: Spatial intersection queries

## Prerequisites

- **DuckDB** with spatial extension
- **GDAL/OGR** tools for GPKG conversion
- **SwissBoundaries3D** data (auto-downloaded if available)

## Examples

```bash
# Complete workflow
./raw_input_data/prepare_data.sh                 # One-time setup
./operations/query_municipality_zones.sh Zug     # Query Zug municipality
./operations/discover_municipalities.sh canton ZG # List all Zug municipalities
./operations/test_queries.sh                     # Verify everything works
```

## Troubleshooting

### Data Not Found
```bash
# If queries fail, ensure data is prepared
./raw_input_data/prepare_data.sh
```

### Missing SwissBoundaries3D
1. Download from [data.geo.admin.ch](https://data.geo.admin.ch)
2. Search for "SwissBoundaries3D"
3. Place GPKG file in `data/input/`

### DuckDB Issues
```bash
# Install spatial extension manually
duckdb -c "INSTALL spatial; LOAD spatial;"
```

## Web Application

In addition to command-line tools, this project includes a **FastAPI web application** with interactive map visualization and **universal municipality filtering**:

```bash
# Start the web application
./start_server.sh
# Access at http://localhost:8000
```

### Key Features
- **Universal Municipality Filter**: All 2,140+ Swiss municipalities with data availability indicators
- **Data Source Integration**: Complete integration with Swiss geodata infrastructure (345 data sources)
- **Smart Availability**: Municipalities are greyed out when no data is available for the selected data source
- **Interactive Map**: OpenLayers with Swiss/OSM background layers
- **ÖREB Integration**: Real-time property restriction lookups from official cantonal services
- **Technical ID System**: Folder structure based on official technical entry IDs
- **Zone Visualization**: View zones on map with detailed information
- **Multiple Data Views**: Table, Summary, and GeoJSON tabs
- **Spatial Queries**: Real-time spatial intersection queries
- **HTMX Interface**: Minimal JavaScript, maximum responsiveness

### Data Source Management
- **Available Sources**: Ready-to-use data sources with local files
- **Planned Sources**: 343 additional data sources from Swiss geodata API
- **Technical IDs**: Each data source uses its official technical entry ID for folder naming
- **API Integration**: Complete metadata from https://api.geobasisdaten.ch/

### ÖREB (Öffentlich-rechtliche Eigentumsbeschränkungen)

Das System bietet vollständige Integration mit den offiziellen kantonalen ÖREB-Diensten:

#### Funktionen
- **Echtzeitabfragen**: Klicken Sie auf die Karte für sofortige Grundstücksinformationen
- **Vollständige Datenextraktion**: Alle verfügbaren ÖREB-Daten aus kantonalen XML-Auszügen
- **Mehrere Zugriffsmethoden**: Web-Interface, API-Zugang und direkte kantonale Service-Links
- **Mehrere Formate**: Zugang zu XML (vollständige Daten), PDF (offizielle Dokumente) und JSON (strukturierte Daten)
- **Professionelle Anzeige**: Informationsdichte, deutschsprachige Benutzeroberfläche

#### Umfassende Datenanzeige
Das System extrahiert und zeigt **alle verfügbaren Daten** aus kantonalen ÖREB-XML-Antworten mit verbesserter Mehrsprachigkeit:

- **Auszug-Metadaten**: Erstellungsdatum, Auszug-Identifikator, Aktualisierungsdatum
- **Themen-Klassifikationen**: Betroffene Themen, nicht betroffene Themen, Themen ohne Daten
- **Erweiterte Grundstücksinformationen**: EGRID, Gemeinde, Kanton, BFS-Nummer, Fläche, Typ
- **Detailliertes Rechtsrahmen**: Beschränkungen mit Flächen und Prozentsätzen, Rechtsvorschriften mit Web-Links
- **Administrative Informationen**: Allgemeine Informationen, Grundlagendaten, Glossar, Gemeinde-Details, zuständige Stelle
- **🖼️ Logos und Bilder**: Vollständige Anzeige aller base64-kodierten Bilder aus XML
  - **Offizielle Logos**: ÖREB-Kataster, Bundes-, Kantons- und Gemeindelogos
  - **QR-Codes**: Verifikations-QR-Codes für Authentizitätsprüfung
  - **Symbole**: Legende-Symbole mit Kontext-Informationen
  - **Karten**: Mehrsprachige Kartenbilder (DE, FR, IT)
- **🌍 Mehrsprachige Unterstützung**: Automatische Erkennung und Anzeige von Deutsch, Französisch, Italienisch, Rätoromanisch
- **📊 Strukturierte Anzeige**: Schema-basierte Darstellung nach offiziellen ÖREB 2.0 Standards

#### Direkte Zugangslinks
- **API-Query-Links**: Direkter Zugang zu unseren verarbeiteten API-Endpunkten
- **Kantonale XML-Links**: Direkter Zugang zu offiziellen XML-Auszügen (mit Bildern)
- **Kantonale PDF-Links**: Direkter Zugang zu offiziellen PDF-Dokumenten
- **Kantonale JSON-Links**: Direkter Zugang zu strukturierten JSON-Daten

#### 🎉 **VOLLSTÄNDIGE SCHWEIZER ÖREB-ABDECKUNG ERREICHT** (Mai 2025)

**🏆 DURCHBRUCH**: **100% ERFOLGSRATE** - Alle 26 Schweizer Kantone haben jetzt funktionierende ÖREB-Dienste!

**Testmethode**: Umfassende Tests mit kantonsspezifischen Koordinaten und verifizierten EGRIDs, Validierung von XML, PDF, JSON und URL-Formaten über alle 104 Endpunkte.

✅ **Vollständig funktionsfähig (19 Kantone - 73%)**:
AI, AR, BE, FR, GE, GR, JU, LU, NE, NW, OW, SG, SZ, TG, TI, UR, VD, VS, ZG, ZH

⚠️ **Teilweise funktionsfähig (6 Kantone - 23%)**:
AG, BL, BS, GL, SH, SO (die meisten Formate funktionieren)

❌ **Nicht funktionsfähig**: 0 Kantone

**Technische Erfolgsraten**:
- **XML-Erfolg**: 100% (26/26 Kantone) - Universelle Abdeckung
- **PDF-Erfolg**: 85% (22/26 Kantone) - Ausgezeichnete Abdeckung
- **JSON-Erfolg**: 85% (22/26 Kantone) - Ausgezeichnete Abdeckung
- **104 getestete Endpunkte**: Vollständige Validierung aller Formatkombinationen

**Bevölkerungsabdeckung**: **100%** - Vollständige Schweizer Bevölkerungsabdeckung
**Geografische Abdeckung**: **100%** - Alle Schweizer Gebiete abgedeckt

*Dies stellt die **erste vollständige Integration** aller Schweizer kantonalen ÖREB-Dienste dar und bietet eine beispiellose nationale Abdeckung für Grundstücksbeschränkungsdaten.*

**📚 Vollständige Dokumentation**: Siehe `docs/OEREB_INTEGRATION.md` für umfassende technische Details, Changelog und Implementierungshinweise.

#### Verwendung
1. Öffnen Sie die Webanwendung: http://localhost:8000
2. Klicken Sie auf eine beliebige Stelle auf der Schweizer Karte
3. Die ÖREB-Informationen werden automatisch in der linken Seitenleiste angezeigt

See `docs/WEB_APPLICATION.md` and `docs/UNIVERSAL_MUNICIPALITY_FILTER.md` for detailed usage instructions.

## Design Philosophy

This project follows a **separation of concerns** approach:

- **Preparation scripts**: Handle data conversion and setup (run once)
- **Operational scripts**: Handle daily queries and analysis
- **Web application**: Interactive visualization and exploration
- **Configuration**: Centralized settings for easy maintenance
- **Standardized paths**: Consistent data organization

This separation makes the project easier to understand, maintain, and extend.
