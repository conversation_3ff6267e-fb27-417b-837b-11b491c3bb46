# Municipality Zones Viewer - Environment Configuration
# Copy this file to .env and modify values as needed

# Application Settings
APP_NAME=Municipality Zones Viewer
DEBUG=false

# Database Settings
DUCKDB_PATH=:memory:
DATA_DIRECTORY=data/processed
BOUNDARIES_DIRECTORY=data/boundaries

# Data Sources
NPL_DATA_PATH=data/processed/npl_files
ZH_DATA_PATH=data/processed/zh_files
SWISSBOUNDARIES_PATH=data/boundaries/swissboundaries.parquet

# Map Settings
DEFAULT_ZOOM=8
DEFAULT_CENTER_LAT=46.8182
DEFAULT_CENTER_LNG=8.2275

# API Settings
MAX_ZONES_PER_REQUEST=1000

# Spatial Filtering
# Minimum percentage of zone area that must be within municipality (0.0-1.0)
SPATIAL_FILTER_THRESHOLD=0.8

# Development Settings (uncomment for development)
# DEBUG=true
