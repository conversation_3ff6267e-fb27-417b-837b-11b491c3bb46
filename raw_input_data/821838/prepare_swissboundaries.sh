#!/bin/bash

# SwissBoundaries3D Data Preparation
# This script ensures SwissBoundaries3D data is available and properly located

set -e  # Exit on any error

echo "=== SwissBoundaries3D Data Preparation ==="
echo ""

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# Check if SwissBoundaries3D data already exists in data/input
TARGET_PATH="$PROJECT_ROOT/data/input/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg"
if [ -f "$TARGET_PATH" ]; then
    echo "✓ SwissBoundaries3D data already available at: $TARGET_PATH"
    
    # Verify the file is valid
    echo "Verifying SwissBoundaries3D data..."
    if ogrinfo "$TARGET_PATH" > /dev/null 2>&1; then
        echo "✓ SwissBoundaries3D data is valid"
        
        # Show available layers
        echo ""
        echo "Available layers in SwissBoundaries3D:"
        ogrinfo "$TARGET_PATH" | grep "1:" | head -10
        echo ""
        echo "SwissBoundaries3D preparation complete!"
    else
        echo "✗ SwissBoundaries3D data appears to be corrupted"
        echo "Please re-download the data from data.geo.admin.ch"
        exit 1
    fi
else
    echo "SwissBoundaries3D data not found in $TARGET_PATH"
    
    # Check if we have the data in the raw_input_data folder
    LOCAL_GPKG="$SCRIPT_DIR/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg"
    if [ -f "$LOCAL_GPKG" ]; then
        echo "Found SwissBoundaries3D data in raw_input_data. Copying to data/input..."
        mkdir -p "$PROJECT_ROOT/data/input"
        cp "$LOCAL_GPKG" "$TARGET_PATH"
        echo "✓ SwissBoundaries3D data copied successfully!"
    else
        echo ""
        echo "To obtain SwissBoundaries3D data:"
        echo "1. Visit: https://data.geo.admin.ch"
        echo "2. Search for 'SwissBoundaries3D'"
        echo "3. Download the GPKG format"
        echo "4. Place the file as: $TARGET_PATH"
        echo ""
        echo "Or if you have the zip file, extract it to data/input/"
        
        # Check if zip file exists and offer to extract
        LOCAL_ZIP="$SCRIPT_DIR/swissboundaries3d_2025-04_2056_5728.gpkg.zip"
        if [ -f "$LOCAL_ZIP" ]; then
            echo ""
            echo "Found SwissBoundaries3D zip file. Extracting..."
            mkdir -p "$PROJECT_ROOT/data/input"
            cd "$PROJECT_ROOT/data/input"
            unzip -o "$LOCAL_ZIP"
            cd "$PROJECT_ROOT"
            
            # Find the extracted GPKG file
            EXTRACTED_GPKG=$(find "$PROJECT_ROOT/data/input" -name "*.gpkg" -type f | grep -i swissboundaries | head -1)
            if [ -n "$EXTRACTED_GPKG" ]; then
                # Rename to standard name if needed
                if [ "$EXTRACTED_GPKG" != "$TARGET_PATH" ]; then
                    mv "$EXTRACTED_GPKG" "$TARGET_PATH"
                fi
                echo "✓ SwissBoundaries3D data extracted and ready!"
            else
                echo "✗ Could not find GPKG file in extracted archive"
                exit 1
            fi
        else
            echo "Please download SwissBoundaries3D data manually."
            exit 1
        fi
    fi
fi

echo ""
echo "=== SwissBoundaries3D Preparation Complete ==="
