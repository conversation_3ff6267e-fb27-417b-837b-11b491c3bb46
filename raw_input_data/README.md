# Raw Input Data Organization

Diese Verzeichnisstruktur organisiert die Rohdaten nach der offiziellen Schweizer Geodateninfrastruktur-Nummerierung.

## Verzeichnisstruktur

### 821838/
**39.3 SwissBoundaries3D** - Verfügbar ✅
- Technische ID: 821838
- Offizielle Schweizer Verwaltungsgrenzen
- Gemeinde-, Kantons- und Landesgrenzen
- Format: GPKG
- Quelle: swisstopo

**Dateien:**
- `swissBOUNDARIES3D_1_5_LV95_LN02.gpkg` - Hauptdatensatz
- `swiss_municipalities_bfs_snapshot_2025-05-05.csv` - BFS-Gemeindeverzeichnis
- `swissboundaries3d_2025-04_2056_5728.gpkg.zip` - Archiv

### 820737/
**73.1 Nutzungsplanung (kantonal/kommunal)** - Verfügbar ✅
- Technische ID: 820737
- Schweizweite Nutzungsplanungsdaten
- Grundnutzung, überlagernde Flächen, Linien und Punkte
- Format: GPKG → Parquet (verarbeitet)
- Quelle: NPL (Nationale Planungslandschaft)

**Dateien:**
- `npl_nutzungsplanung_lv95/` - Entpackter Datensatz
- `npl_nutzungsplanung_lv95.zip` - Original-Archiv
- `npl_nutzungsplanung_lv95/geopackage/npl_nutzungsplanung_v1_2_2056.gpkg` - Hauptdatei

### oereb_zh_experimental/
**ÖREB-Kataster Kanton Zürich** - Experimentell ⚠️
- Spezifische Daten für Kanton Zürich
- Noch nicht vollständig integriert
- Verschiedene Themen: Nutzungsplanung, Abstandslinien, Gewässerraum, etc.

**Dateien:**
- `OREB-Kataster_-_Nutzungsplanung_-OGD/` - Verschiedene ÖREB-Themen
- `ktZH_ueberMapsZhCh.zip` - Original-Archiv

## Namenskonvention

Die Verzeichnisnamen folgen dem Schema: `{labelNumber}_{kurzer_name}` oder `{technicalEntries.id}`

- `labelNumber`: Offizielle Nummer aus der Schweizer Geodateninfrastruktur (z.B. 39, 73)
- `kurzer_name`: Beschreibender Name ohne Sonderzeichen
- `technicalEntries.id`: Bei mehreren technischen Datensätzen pro legislativem Eintrag wird die technische ID verwendet

**Beispiele:**
- `39_swissboundaries` - Für den gesamten legislativen Eintrag 39
- `821832` - Für den spezifischen technischen Datensatz mit ID 821832 (39.1 Hoheitsgrenzen)
- `73_nutzungsplanung` - Für den gesamten legislativen Eintrag 73

## Hinzufügen neuer Datenquellen

1. **Verzeichnis erstellen**: `{labelNumber}_{name}/`
2. **Daten ablegen**: Original-Dateien und Archive
3. **README aktualisieren**: Neue Quelle dokumentieren
4. **Service konfigurieren**: `DataSourceService` erweitern
5. **Verarbeitung**: Daten für DuckDB vorbereiten

## Status-Legende

- ✅ **Verfügbar**: Vollständig integriert und funktionsfähig
- ⚠️ **Experimentell**: Teilweise implementiert, in Entwicklung
- 📋 **Geplant**: Identifiziert, aber noch nicht implementiert

## Verarbeitung

Die Rohdaten werden durch Skripte in diesem Verzeichnis verarbeitet:
- **Master-Skript**: `prepare_data.sh` - Orchestriert alle Verarbeitungsschritte
- **820737/**: NPL-spezifische Konvertierungsskripte (GPKG → GeoJSON → Parquet)
- **821838/**: SwissBoundaries3D-Vorbereitung
- Konvertierung zu Parquet-Format
- Räumliche Indexierung
- DuckDB-Optimierung
- Metadaten-Extraktion

### Verwendung
```bash
# Alle Daten vorbereiten
./raw_input_data/prepare_data.sh
```

Verarbeitete Daten landen in `data/processed/` und sind über die Anwendung verfügbar.
