#!/bin/bash

# Master Data Preparation Script
# This script orchestrates all data preparation steps for the spatial analysis project
# Run this once to prepare all data for subsequent operations

set -e  # Exit on any error

echo "=== SPATIAL DATA PREPARATION MASTER SCRIPT ==="
echo "This script will prepare all spatial data for analysis."
echo ""

# Get script directory (now in raw_input_data)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Load configuration
source "$PROJECT_ROOT/config/data_sources.conf"

echo "Step 1: Preparing directory structure..."
mkdir -p "$PROJECT_ROOT/data/input" "$PROJECT_ROOT/data/processed/geojson" "$PROJECT_ROOT/data/processed/parquet"

echo "Step 2: Moving raw data to standardized locations..."
# Move raw data to data/input if not already there
if [ -d "$SCRIPT_DIR" ] && [ ! -d "$PROJECT_ROOT/data/input" ]; then
    # Copy instead of move to preserve raw_input_data structure
    cp -r "$SCRIPT_DIR"/* "$PROJECT_ROOT/data/input/" 2>/dev/null || true
fi

echo "Step 3: Converting GPKG files to GeoJSON (preserves curved geometries)..."
"$SCRIPT_DIR/820737/convert_gpkg_to_geojson.sh"

echo "Step 4: Converting GeoJSON files to Parquet (efficient storage)..."
"$SCRIPT_DIR/820737/convert_geojson_to_parquet.sh"

echo "Step 5: Preparing SwissBoundaries3D data..."
"$SCRIPT_DIR/821838/prepare_swissboundaries.sh"

echo ""
echo "=== DATA PREPARATION COMPLETE ==="
echo ""
echo "Prepared data locations:"
echo "  - Parquet files: data/processed/parquet/"
echo "  - GeoJSON files: data/processed/geojson/"
echo "  - SwissBoundaries: data/input/"
echo ""
echo "You can now run operational scripts from the operations/ directory."
echo ""
echo "Next steps:"
echo "  1. Discover municipalities: ./operations/discover_municipalities.sh"
echo "  2. Query zones by municipality: ./operations/query_municipality_zones.sh [municipality_name]"
echo ""
