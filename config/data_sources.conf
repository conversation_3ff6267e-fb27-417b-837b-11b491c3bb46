# Data Sources Configuration
# This file contains all paths and settings for the zone filtering scripts

# === FILE PATHS ===

# NPL (Nutzungsplanung) data files - now using GPKG directly
NPL_GRUNDNUTZUNG_GPKG="raw_input_data/820737/npl_nutzungsplanung_lv95/geopackage/npl_nutzungsplanung_v1_2_2056.gpkg"
NPL_GRUNDNUTZUNG_LAYER="grundnutzung"

# Zurich canton specific files
ZH_ZONENFLAECHE_PARQUET="data/processed/parquet/zh_files/NP_GN_Zonenflaeche.parquet"

# SwissBoundaries3D data
SWISSBOUNDARIES_GPKG="data/input/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg"
SWISSBOUNDARIES_MUNICIPALITY_LAYER="tlm_hoheitsgebiet"
SWISSBOUNDARIES_CANTON_LAYER="tlm_kantonsgebiet"

# === COLUMN NAMES ===

# NPL data columns
NPL_CANTON_COLUMN="kanton"
NPL_KOMMUNAL_ZONE_COLUMN="typ_kommunal_bezeichnung"
NPL_KANTONAL_ZONE_COLUMN="typ_kantonal_bezeichnung"
NPL_KOMMUNAL_CODE_COLUMN="typ_kommunal_code"
NPL_REMARKS_COLUMN="bemerkungen"
NPL_GEOMETRY_COLUMN="geom"

# SwissBoundaries3D columns
SB_MUNICIPALITY_NAME_COLUMN="name"
SB_BFS_NUMBER_COLUMN="bfs_nummer"
SB_CANTON_NUMBER_COLUMN="kantonsnummer"
SB_GEOMETRY_COLUMN="geom"

# === CANTON CODES ===
# Swiss canton abbreviations
AVAILABLE_CANTONS="AG AI AR BE BL BS FR GE GL GR JU LU NE NW OW SG SH SO SZ TG TI UR VD VS ZG ZH"

# === KNOWN MUNICIPALITIES ===
# Format: "Municipality_Name:BFS_Code:Canton"
# This is a sample list - the system can work with any municipality in SwissBoundaries3D
KNOWN_MUNICIPALITIES="
Steinhausen:1708:ZG
Zug:1711:ZG
Baar:1701:ZG
Cham:1702:ZG
Hünenberg:1703:ZG
Menzingen:1704:ZG
Neuheim:1705:ZG
Oberägeri:1706:ZG
Risch:1707:ZG
Unterägeri:1709:ZG
Walchwil:1710:ZG
Zurich:261:ZH
Winterthur:230:ZH
Basel:2701:BS
Bern:351:BE
Geneva:6621:GE
Lausanne:5586:VD
Lucerne:1061:LU
St. Gallen:3203:SG
"

# === QUERY SETTINGS ===

# Default timeout for spatial queries (seconds)
SPATIAL_QUERY_TIMEOUT=60

# Maximum number of results to display
MAX_RESULTS=100

# === DuckDB SETTINGS ===

# DuckDB extensions to load
DUCKDB_EXTENSIONS="spatial"

# DuckDB memory limit (optional)
# DUCKDB_MEMORY_LIMIT="4GB"
