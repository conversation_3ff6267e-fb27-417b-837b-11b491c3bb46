"""
Application-specific settings.
"""

from pydantic_settings import BaseSettings
from typing import Dict, Any

class AppSettings(BaseSettings):
    """Application-specific configuration."""
    
    # Map settings
    map_config: Dict[str, Any] = {
        "default_center": [8.2275, 46.8182],  # Switzerland center
        "default_zoom": 8,
        "min_zoom": 6,
        "max_zoom": 18,
        "max_bounds": [
            [5.9559, 45.8180],  # Southwest corner
            [10.4921, 47.8084]  # Northeast corner
        ]
    }
    
    # Zone styling
    zone_styles: Dict[str, Dict[str, Any]] = {
        "grundnutzung": {
            "fill_color": "#3498db",
            "fill_opacity": 0.6,
            "stroke_color": "#2c3e50",
            "stroke_width": 1
        },
        "ueberlagernde_flaechen": {
            "fill_color": "#e74c3c",
            "fill_opacity": 0.5,
            "stroke_color": "#c0392b",
            "stroke_width": 1
        },
        "ueberlagernde_linien": {
            "fill_color": "#f39c12",
            "fill_opacity": 0.7,
            "stroke_color": "#e67e22",
            "stroke_width": 2
        },
        "ueberlagernde_punkte": {
            "fill_color": "#9b59b6",
            "fill_opacity": 0.8,
            "stroke_color": "#8e44ad",
            "stroke_width": 1
        }
    }
    
    # API limits
    api_limits: Dict[str, int] = {
        "max_zones_per_request": 1000,
        "max_search_results": 50,
        "default_page_size": 100
    }
    
    # Feature flags
    features: Dict[str, bool] = {
        "enable_caching": True,
        "enable_search": True,
        "enable_export": True,
        "enable_analytics": False
    }
    
    class Config:
        env_prefix = "APP_"
        case_sensitive = False

# Global app settings instance
app_settings = AppSettings()
