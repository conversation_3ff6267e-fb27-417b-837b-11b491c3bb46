"""
Database configuration settings.
"""

from pydantic_settings import BaseSettings
from typing import Dict, List, Any
import os

class DatabaseConfig(BaseSettings):
    """Database configuration."""

    # DuckDB settings
    duckdb_path: str = ":memory:"
    duckdb_config: Dict[str, str] = {
        "memory_limit": "2GB",
        "threads": "4",
        "max_memory": "80%"
    }

    # GPKG file path for NPL data
    gpkg_file_path: str = "raw_input_data/820737/npl_nutzungsplanung_lv95/geopackage/npl_nutzungsplanung_v1_2_2056.gpkg"

    # Layer mappings for GPKG file
    gpkg_layers: Dict[str, str] = {
        "npl_grundnutzung": "grundnutzung",
        "npl_ueberlagernde_flaechen": "ueberlagernde_nutzungsplaninhalte_flaechen",
        "npl_ueberlagernde_linien": "ueberlagernde_nutzungsplaninhalte_linien",
        "npl_ueberlagernde_punkte": "ueberlagernde_nutzungsplaninhalte_punkte"
    }

    # Legacy data file paths (non-NPL sources)
    data_files: Dict[str, str] = {
        "zh_zonenflaeche": "data/processed/zh_files/NP_GN_Zonenflaeche.parquet",
        "zh_zonenflaeche_geometrie": "data/processed/zh_files/NP_GN_Zonenflaeche_Geometrie.parquet",
        "zh_ueberlagernde_flaeche": "data/processed/zh_files/NP_UL_Flaeche.parquet",
        "zh_ueberlagernde_flaeche_geometrie": "data/processed/zh_files/NP_UL_Flaeche_Geometrie.parquet",
        "swiss_boundaries": "data/boundaries/swissboundaries.parquet"
    }

    # View definitions
    view_definitions: Dict[str, str] = {
        "all_grundnutzung": """
            SELECT
                'npl' as source,
                typ_kommunal_bezeichnung as zone_name,
                typ_kommunal_code as municipality_code,
                geom,
                ST_Area(geom) as area
            FROM npl_grundnutzung
            WHERE geom IS NOT NULL
        """,
        "all_municipalities": """
            SELECT DISTINCT
                tlm_hoheitsgebiet_name as name,
                tlm_hoheitsgebiet_name as id,
                'CH' as canton,
                geometry,
                ST_Centroid(geometry) as centroid,
                ST_Area(geometry) as area
            FROM swiss_municipalities
            WHERE tlm_hoheitsgebiet_name IS NOT NULL
        """
    }

    # Spatial reference systems
    srid_config: Dict[str, int] = {
        "swiss_lv95": 2056,
        "wgs84": 4326,
        "web_mercator": 3857
    }

    # Performance settings
    performance: Dict[str, Any] = {
        "enable_parallel_processing": True,
        "chunk_size": 10000,
        "cache_size": "1GB",
        "enable_statistics": True
    }

    class Config:
        env_prefix = "DB_"
        case_sensitive = False

    def get_data_file_path(self, key: str) -> str:
        """Get full path to a data file."""
        return self.data_files.get(key, "")

    def file_exists(self, key: str) -> bool:
        """Check if a data file exists."""
        path = self.get_data_file_path(key)
        return os.path.exists(path) if path else False

# Global database config instance
db_config = DatabaseConfig()
