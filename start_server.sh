#!/bin/bash

# Municipality Zones Viewer - Development Server Startup Script

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Kill any existing uvicorn processes on port 8000
echo "Checking for existing processes on port 8000..."
pkill -f "uvicorn.*app.main:app.*8000" 2>/dev/null || true

# Wait a moment for processes to terminate
sleep 2

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "Virtual environment not found. Creating .venv..."
    python3 -m venv .venv
    echo "Installing dependencies..."
    .venv/bin/pip install -r requirements.txt
fi

# Activate virtual environment
echo "Activating virtual environment..."
source .venv/bin/activate

# Load environment variables
if [ -f ".env" ]; then
    echo "Loading environment variables from .env..."
    export $(grep -v '^#' .env | xargs)
fi

# Start the server
echo "Starting FastAPI server on http://localhost:8000..."
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
