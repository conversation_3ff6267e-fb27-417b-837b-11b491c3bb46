# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Logs
*.log
logs/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# Python version management
.python-version

# Package managers
Pipfile.lock
poetry.lock
pdm.lock
.pdm.toml
__pypackages__/

# Environments
.env.local
.env.production
.env.test
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE and editors
.idea/
.vscode/
.spyderproject
.spyproject
.ropeproject

# Type checkers
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/
.pytype/

# Documentation
docs/_build/
/site

# Cython debug symbols
cython_debug/

# FastAPI / Web application specific
*.duckdb
*.db
*.sqlite
*.sqlite3
*.sqlite3-journal
temp/
cache/
uploads/
static/uploads/

# Spatial data and processing
*.gpkg-wal
*.gpkg-shm
*.shp.xml
*.qpj

# GeoJSON output (if large)
geojson_output/*.geojson
geojson_output/*.json

# Processed data (uncomment if you don't want to track processed files)
# data/processed/
# data/boundaries/

# Raw input data (uncomment if files are too large for git)
# raw_input_data/

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*~

# Temporary files
*.tmp
*.temp
*.swp
*.swo
