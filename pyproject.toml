[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "municipality-zones-viewer"
version = "1.0.0"
description = "Swiss Municipality Zones (Nutzungsplan) visualization with spatial queries"
authors = [
    {name = "Municipality Zones Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    # FastAPI and web framework dependencies
    "fastapi>=0.115.12",
    "uvicorn[standard]>=0.34.2",
    "jinja2>=3.1.6",

    # Database and spatial dependencies
    "duckdb>=1.3.0",

    # Configuration and validation
    "pydantic>=2.11",
    "pydantic-settings>=2.1.0",

    # HTTP client for ÖREB services
    "httpx>=0.28.1",

    # Testing
    "pytest>=7.4.3",
]

[project.optional-dependencies]
dev = [
    "black>=24.10.0",
    "flake8>=7.1.0",
    "mypy>=1.15.0",
]

[project.urls]
Homepage = "http://localhost:8000"
Repository = ""
Documentation = ""

[project.scripts]
municipality-zones = "app.main:app"

[tool.setuptools.packages.find]
where = ["."]
include = ["app*", "config*"]
exclude = ["tests*"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "duckdb.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    ".eggs",
    "*.egg",
]
