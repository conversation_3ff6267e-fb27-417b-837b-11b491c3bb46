"""
Spatial service for geographic operations.
"""

from typing import List, Dict, Any
from fastapi import Depends

from app.core.database import get_db_dependency
from app.core.exceptions import SpatialQueryError

class SpatialService:
    """Service for spatial operations."""

    def __init__(self, db = Depends(get_db_dependency)):
        self.db = db

    async def search_locations(self, query: str) -> List[Dict[str, Any]]:
        """Search for locations (municipalities, addresses, etc.)."""
        try:
            # Search municipalities - transform coordinates to WGS84
            # Note: ST_Transform from EPSG:2056 to EPSG:4326 returns (lat, lon) so we need to swap
            municipality_query = """
                SELECT
                    tlm_hoheitsgebiet_name as name,
                    'municipality' as type,
                    ST_Y(ST_Transform(ST_Centroid(geometry), 'EPSG:2056', 'EPSG:4326')) as lng,
                    ST_X(ST_Transform(ST_Centroid(geometry), 'EPSG:2056', 'EPSG:4326')) as lat
                FROM swiss_municipalities
                WHERE tlm_hoheitsgebiet_name ILIKE ?
                LIMIT 10
            """

            search_term = f"%{query}%"
            result = self.db.execute(municipality_query, [search_term]).fetchall()

            locations = []
            for row in result:
                locations.append({
                    "name": row[0],
                    "type": row[1],
                    "lng": row[2],
                    "lat": row[3]
                })

            return locations

        except Exception as e:
            raise SpatialQueryError(f"Error searching locations: {str(e)}")

    async def get_zones_at_point(self, lng: float, lat: float) -> List[Dict[str, Any]]:
        """Get zones that contain a specific point."""
        try:
            point_wkt = f"POINT({lng} {lat})"

            # Query grundnutzung zones
            query = """
                SELECT
                    typ_bezeichnung as zone_name,
                    typ_kommunal_code as municipality_code,
                    'grundnutzung' as zone_type
                FROM npl_grundnutzung
                WHERE ST_Contains(geometry, ST_GeomFromText(?))
            """

            result = self.db.execute(query, [point_wkt]).fetchall()

            zones = []
            for row in result:
                zones.append({
                    "zone_name": row[0],
                    "municipality_code": row[1],
                    "zone_type": row[2]
                })

            return zones

        except Exception as e:
            raise SpatialQueryError(f"Error querying zones at point: {str(e)}")

    async def get_zones_in_bounds(
        self,
        min_lng: float,
        min_lat: float,
        max_lng: float,
        max_lat: float
    ) -> Dict[str, Any]:
        """Get zones within geographic bounds as GeoJSON."""
        try:
            bbox_wkt = f"POLYGON(({min_lng} {min_lat}, {max_lng} {min_lat}, {max_lng} {max_lat}, {min_lng} {max_lat}, {min_lng} {min_lat}))"

            query = """
                SELECT
                    ROW_NUMBER() OVER() as id,
                    typ_bezeichnung as zone_name,
                    typ_kommunal_code as municipality_code,
                    ST_AsGeoJSON(ST_Intersection(geometry, ST_GeomFromText(?))) as geometry
                FROM npl_grundnutzung
                WHERE ST_Intersects(geometry, ST_GeomFromText(?))
                LIMIT 1000
            """

            result = self.db.execute(query, [bbox_wkt, bbox_wkt]).fetchall()

            features = []
            for row in result:
                if row[3]:  # Only include if geometry exists
                    features.append({
                        "type": "Feature",
                        "properties": {
                            "id": row[0],
                            "zone_name": row[1],
                            "municipality_code": row[2]
                        },
                        "geometry": row[3]
                    })

            return {
                "type": "FeatureCollection",
                "features": features
            }

        except Exception as e:
            raise SpatialQueryError(f"Error querying zones in bounds: {str(e)}")
