"""
Data source service for managing Swiss geodata sources.
"""

import json
import httpx
from typing import List, Optional
from pathlib import Path
import duckdb

from app.models.data_source import (
    DataSourceInfo, DataSourceList, LocalDataSource, LegislativeEntry,
    TechnicalEntry, DataSourceStatus, GeoCategory
)
from app.core.config import Settings
from app.services.canton_service import CantonService


class DataSourceService:
    """Service for managing data sources."""

    def __init__(self, settings: Settings = None, db: duckdb.DuckDBPyConnection = None):
        self.settings = settings or Settings()
        self.raw_data_path = Path("raw_input_data")
        self.cache_path = Path("data") / "cache"
        self.cache_path.mkdir(parents=True, exist_ok=True)
        self.db = db
        self.canton_service = CantonService(db) if db else None

        self.local_sources = {}

        # Local source configurations
        self.local_source_configs = [
            {
                "labelNumber": 73,
                "name": "Nutzungsplanung (kantonal/kommunal)",
                "description": "Nutzungsplanung (kantonal/kommunal)",
                "file_paths": [
                    "raw_input_data/820737/npl_nutzungsplanung_lv95/geopackage/npl_nutzungsplanung_v1_2_2056.gpkg"
                ],
                "metadata": {"source": "Geodienste", "format": "gpkg"}
            },
            {
                "labelNumber": 39,
                "technicalLabelNumber": 3,
                "name": "SwissBoundaries3D",
                "description": "SwissBoundaries3D",
                "file_paths": [
                    "raw_input_data/821838/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg",
                    "raw_input_data/821838/swiss_municipalities_bfs_snapshot_2025-05-05.csv"
                ],
                "metadata": {"source": "swisstopo", "format": "gpkg"}
            }
        ]

    async def get_legislative_entries(self, use_cache: bool = True) -> List[LegislativeEntry]:
        """Fetch legislative entries from Swiss geodata API."""
        cache_file = self.cache_path / "legislative_entries.json"

        # Try to load from cache first
        if use_cache and cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return [LegislativeEntry(**entry) for entry in data]
            except Exception:
                pass

        # Fetch from API
        async with httpx.AsyncClient() as client:
            response = await client.get("https://api.geobasisdaten.ch/api/v1/data/?format=json")
            response.raise_for_status()
            api_data = response.json()

        # Convert to our models
        legislative_entries = []
        for entry_data in api_data:
            # Convert technical entries
            tech_entries = []
            for tech_data in entry_data.get("technicalEntries", []):
                # Convert geoCategory if present
                geo_category = None
                if "geoCategory" in tech_data and tech_data["geoCategory"]:
                    geo_cat_data = tech_data["geoCategory"]
                    geo_category = GeoCategory(
                        id=geo_cat_data["id"],
                        code=geo_cat_data["code"],
                        name=geo_cat_data["name"],
                        nameDe=geo_cat_data.get("nameDe"),
                        nameFr=geo_cat_data.get("nameFr"),
                        nameIt=geo_cat_data.get("nameIt"),
                        nameRm=geo_cat_data.get("nameRm")
                    )

                tech_entry = TechnicalEntry(
                    id=tech_data["id"],
                    label=tech_data["label"],
                    labelNumber=tech_data["labelNumber"],
                    description=tech_data.get("description", ""),
                    descriptionDe=tech_data.get("descriptionDe"),
                    descriptionFr=tech_data.get("descriptionFr"),
                    descriptionIt=tech_data.get("descriptionIt"),
                    dataModelUrl=tech_data.get("dataModelUrl"),
                    modelDocumentationUrl=tech_data.get("modelDocumentationUrl"),
                    metadataUrls=tech_data.get("metadataUrls", []),
                    geoCategory=geo_category,
                    startDate=tech_data.get("startDate"),
                    endDate=tech_data.get("endDate"),
                    status=tech_data.get("status"),
                    isHistorised=tech_data.get("isHistorised", False),
                    hideLabel=tech_data.get("hideLabel", False),
                    dataOwner=tech_data.get("dataOwner"),
                    dataProvider=tech_data.get("dataProvider"),
                    contactPerson=tech_data.get("contactPerson"),
                    contactEmail=tech_data.get("contactEmail"),
                    contactPhone=tech_data.get("contactPhone"),
                    updateFrequency=tech_data.get("updateFrequency"),
                    spatialExtent=tech_data.get("spatialExtent"),
                    temporalExtent=tech_data.get("temporalExtent"),
                    lineage=tech_data.get("lineage"),
                    resolution=tech_data.get("resolution"),
                    accuracy=tech_data.get("accuracy")
                )
                tech_entries.append(tech_entry)

            # Create legislative entry
            legislative_entry = LegislativeEntry(
                id=entry_data["id"],
                labelNumber=entry_data["labelNumber"],
                identifier=entry_data["identifier"],
                title=entry_data.get("title", ""),
                titleDe=entry_data.get("titleDe"),
                titleFr=entry_data.get("titleFr"),
                titleIt=entry_data.get("titleIt"),
                technicalEntries=tech_entries,
                downloadService=entry_data.get("downloadService", False),
                access=entry_data.get("access", "public"),
                oereb=entry_data.get("oereb", False)
            )
            legislative_entries.append(legislative_entry)

        # Cache the results
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump([entry.dict() for entry in legislative_entries], f, ensure_ascii=False, indent=2)
        except Exception:
            pass

        return legislative_entries

    async def _initialize_local_sources(self, legislative_entries: List[LegislativeEntry]):
        """Initialize local sources with technical entry IDs from API data."""
        if self.local_sources:
            return

        for config in self.local_source_configs:
            # Find matching legislative entry
            legislative = next(
                (entry for entry in legislative_entries if entry.labelNumber == config["labelNumber"]),
                None
            )

            if not legislative:
                continue

            # Find matching technical entry
            tech_entry = None
            if "technicalLabelNumber" in config:
                tech_entry = next(
                    (te for te in legislative.technicalEntries if te.labelNumber == config["technicalLabelNumber"]),
                    None
                )
            else:
                tech_entry = legislative.technicalEntries[0] if legislative.technicalEntries else None

            if tech_entry:
                local_source = LocalDataSource(
                    id=str(tech_entry.id),
                    name=f"{legislative.labelNumber}.{tech_entry.labelNumber} {config['name']}",
                    description=config["description"],
                    status=DataSourceStatus.AVAILABLE,
                    file_paths=config["file_paths"],
                    legislative_entry_id=legislative.id,
                    technical_entry_id=tech_entry.id,
                    metadata=config["metadata"]
                )
                self.local_sources[str(tech_entry.id)] = local_source

    async def get_data_sources(self) -> DataSourceList:
        """Get all data sources with availability information."""
        # Get legislative entries from API
        legislative_entries = await self.get_legislative_entries()

        await self._initialize_local_sources(legislative_entries)

        sources = []
        used_tech_ids = set()
        for local_source in self.local_sources.values():
            legislative = None
            for leg_entry in legislative_entries:
                for tech_entry in leg_entry.technicalEntries:
                    if tech_entry.id == local_source.technical_entry_id:
                        legislative = LegislativeEntry(
                            id=tech_entry.id,
                            labelNumber=leg_entry.labelNumber,
                            identifier=f"{leg_entry.identifier}.{tech_entry.labelNumber}",
                            title=leg_entry.title,
                            titleDe=leg_entry.titleDe,
                            titleFr=leg_entry.titleFr,
                            titleIt=leg_entry.titleIt,
                            technicalEntries=[tech_entry],
                            downloadService=leg_entry.downloadService,
                            access=leg_entry.access,
                            oereb=leg_entry.oereb
                        )
                        used_tech_ids.add(tech_entry.id)
                        break
                if legislative:
                    break

            # Get canton availability information if canton service is available
            canton_info = None
            canton_summary = None
            if self.canton_service and local_source:
                try:
                    canton_info = await self.canton_service.get_canton_availability_for_data_source(str(local_source.technical_entry_id))
                    canton_summary = await self.canton_service.get_canton_summary_for_data_source(str(local_source.technical_entry_id))
                except Exception as e:
                    canton_info = None
                    canton_summary = None

            source_info = DataSourceInfo(
                local=local_source,
                legislative=legislative,
                canton_info=canton_info,
                canton_summary=canton_summary
            )
            sources.append(source_info)


        for legislative in legislative_entries:
            if legislative.technicalEntries:
                for tech_entry in legislative.technicalEntries:
                    if tech_entry.id not in used_tech_ids:
                        legislative_copy = LegislativeEntry(
                            id=tech_entry.id,
                            labelNumber=legislative.labelNumber,
                            identifier=f"{legislative.identifier}.{tech_entry.labelNumber}",
                            title=legislative.title,
                            titleDe=legislative.titleDe,
                            titleFr=legislative.titleFr,
                            titleIt=legislative.titleIt,
                            technicalEntries=[tech_entry],
                            downloadService=legislative.downloadService,
                            access=legislative.access,
                            oereb=legislative.oereb
                        )

                        source_info = DataSourceInfo(
                            local=None,
                            legislative=legislative_copy
                        )
                        sources.append(source_info)
            else:
                if legislative.id not in used_tech_ids:
                    source_info = DataSourceInfo(
                        local=None,
                        legislative=legislative
                    )
                    sources.append(source_info)

        # Sort by label number and technical entry number
        sources.sort(key=lambda x: (
            x.legislative.labelNumber if x.legislative else 999,
            x.legislative.technicalEntries[0].labelNumber if x.legislative and x.legislative.technicalEntries else 999
        ))

        # Count availability
        available_count = sum(1 for s in sources if s.is_available)
        unavailable_count = len(sources) - available_count

        return DataSourceList(
            sources=sources,
            total=len(sources),
            available_count=available_count,
            unavailable_count=unavailable_count
        )

    async def get_data_source_by_id(self, source_id: str) -> Optional[DataSourceInfo]:
        """Get a specific data source by ID (technical entry ID)."""
        # Get legislative entries and initialize local sources
        legislative_entries = await self.get_legislative_entries()
        await self._initialize_local_sources(legislative_entries)

        # Try technical entry ID
        try:
            tech_id = int(source_id)

            # Check if this is a local source
            local_source = self.local_sources.get(source_id)

            # Find the technical entry in API data
            for legislative in legislative_entries:
                for tech_entry in legislative.technicalEntries:
                    if tech_entry.id == tech_id:
                        # Create a legislative entry with only this technical entry
                        legislative_copy = LegislativeEntry(
                            id=tech_entry.id,
                            labelNumber=legislative.labelNumber,
                            identifier=f"{legislative.identifier}.{tech_entry.labelNumber}",
                            title=legislative.title,
                            titleDe=legislative.titleDe,
                            titleFr=legislative.titleFr,
                            titleIt=legislative.titleIt,
                            technicalEntries=[tech_entry],
                            downloadService=legislative.downloadService,
                            access=legislative.access,
                            oereb=legislative.oereb
                        )

                        # Get canton availability information if canton service is available
                        canton_info = None
                        canton_summary = None
                        if self.canton_service and local_source:
                            try:
                                canton_info = await self.canton_service.get_canton_availability_for_data_source(source_id)
                                canton_summary = await self.canton_service.get_canton_summary_for_data_source(source_id)
                            except Exception as e:
                                canton_info = None
                                canton_summary = None

                        return DataSourceInfo(
                            local=local_source,
                            legislative=legislative_copy,
                            canton_info=canton_info,
                            canton_summary=canton_summary
                        )
        except ValueError:
            pass

        return None
