"""
Canton mapping and availability service for Swiss cantons.
"""

from typing import Dict, Optional
import duckdb
import json
from datetime import datetime, timedelta
from pathlib import Path
from app.models.data_source import CantonAvailability, DataSourceCantonInfo


class CantonService:
    """Service for canton-related operations."""

    # Swiss canton mapping: number -> (abbreviation, name)
    CANTON_MAPPING = {
        1: ("ZH", "Zürich"),
        2: ("BE", "Bern"),
        3: ("LU", "Luzern"),
        4: ("UR", "Uri"),
        5: ("SZ", "Schwyz"),
        6: ("OW", "Obwalden"),
        7: ("NW", "Nidwalden"),
        8: ("GL", "Glarus"),
        9: ("ZG", "Zug"),
        10: ("FR", "Fribourg"),
        11: ("SO", "Solothurn"),
        12: ("BS", "Basel-Stadt"),
        13: ("BL", "Basel-Landschaft"),
        14: ("<PERSON>", "Schaffhausen"),
        15: ("AR", "Appenzell Ausserrhoden"),
        16: ("AI", "Appenzell Innerrhoden"),
        17: ("SG", "St. Gallen"),
        18: ("GR", "Graubünden"),
        19: ("AG", "Aargau"),
        20: ("TG", "Thurgau"),
        21: ("TI", "Ticino"),
        22: ("VD", "Vaud"),
        23: ("VS", "Valais"),
        24: ("NE", "Neuchâtel"),
        25: ("GE", "Genève"),
        26: ("JU", "Jura"),
    }

    def __init__(self, db: duckdb.DuckDBPyConnection):
        self.db = db
        self.cache_dir = Path("data/cache")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.cache_file = self.cache_dir / "canton_availability.json"
        self.cache_expiry_hours = 24  # Cache expires after 24 hours
        self._cache = self._load_cache()

    def get_canton_abbreviation(self, canton_number: int) -> str:
        """Get canton abbreviation from canton number."""
        return self.CANTON_MAPPING.get(canton_number, ("", ""))[0]

    def get_canton_name(self, canton_number: int) -> str:
        """Get canton name from canton number."""
        return self.CANTON_MAPPING.get(canton_number, ("", ""))[1]

    def _load_cache(self) -> Dict:
        """Load cache from file."""
        if not self.cache_file.exists():
            return {}

        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)

            # Check if cache is expired
            if 'timestamp' in cache_data:
                cache_time = datetime.fromisoformat(cache_data['timestamp'])
                if datetime.now() - cache_time > timedelta(hours=self.cache_expiry_hours):
                    return {}

            return cache_data.get('data', {})
        except Exception:
            return {}

    def _save_cache(self):
        """Save cache to file."""
        try:
            cache_data = {
                'timestamp': datetime.now().isoformat(),
                'data': self._cache
            }
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
        except Exception:
            pass  # Fail silently if cache save fails

    def _get_cache_key(self, data_source_id: str) -> str:
        """Generate cache key for data source."""
        return f"canton_availability_{data_source_id}"

    def invalidate_cache(self, data_source_id: Optional[str] = None):
        """Invalidate cache for specific data source or all cache."""
        if data_source_id:
            cache_key = self._get_cache_key(data_source_id)
            self._cache.pop(cache_key, None)
        else:
            self._cache.clear()
        self._save_cache()

    async def get_canton_availability_for_data_source(self, data_source_id: str) -> Optional[DataSourceCantonInfo]:
        """Get canton availability information for a specific data source."""
        try:
            # Check cache first
            cache_key = self._get_cache_key(data_source_id)
            if cache_key in self._cache:
                cached_data = self._cache[cache_key]
                return self._deserialize_canton_info(cached_data)

            # Map data source IDs to their data paths and types
            data_source_mapping = {
                "820737": {  # Geodienste Nutzungsplanung
                    "type": "gpkg",
                    "path": "raw_input_data/820737/npl_nutzungsplanung_lv95/geopackage/npl_nutzungsplanung_v1_2_2056.gpkg",
                    "layer": "grundnutzung"
                },
                "821838": {  # SwissBoundaries3D - all cantons available
                    "type": "boundaries",
                    "path": "raw_input_data/821838/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg"
                }
            }

            data_config = data_source_mapping.get(data_source_id)
            if not data_config:
                return None

            # Calculate canton availability
            if data_config["type"] == "boundaries":
                canton_info = await self._get_all_cantons_available()
            else:
                canton_info = await self._calculate_canton_availability(data_config)

            # Cache the result
            if canton_info:
                self._cache[cache_key] = self._serialize_canton_info(canton_info)
                self._save_cache()

            return canton_info

        except Exception:
            # Return None if there's an error - this will hide canton info
            return None

    def _serialize_canton_info(self, canton_info: DataSourceCantonInfo) -> Dict:
        """Serialize canton info for caching."""
        return {
            'total_cantons': canton_info.total_cantons,
            'cantons_with_50_percent_plus': canton_info.cantons_with_50_percent_plus,
            'cantons_with_data': [
                {
                    'canton_abbreviation': c.canton_abbreviation,
                    'canton_name': c.canton_name,
                    'municipalities_with_data': c.municipalities_with_data,
                    'total_municipalities': c.total_municipalities,
                    'coverage_percentage': c.coverage_percentage
                }
                for c in canton_info.cantons_with_data
            ],
            'cantons_without_data': [
                {
                    'canton_abbreviation': c.canton_abbreviation,
                    'canton_name': c.canton_name,
                    'municipalities_with_data': c.municipalities_with_data,
                    'total_municipalities': c.total_municipalities,
                    'coverage_percentage': c.coverage_percentage
                }
                for c in canton_info.cantons_without_data
            ]
        }

    def _deserialize_canton_info(self, data: Dict) -> DataSourceCantonInfo:
        """Deserialize canton info from cache."""
        cantons_with_data = [
            CantonAvailability(**c) for c in data.get('cantons_with_data', [])
        ]
        cantons_without_data = [
            CantonAvailability(**c) for c in data.get('cantons_without_data', [])
        ]

        return DataSourceCantonInfo(
            total_cantons=data['total_cantons'],
            cantons_with_50_percent_plus=data['cantons_with_50_percent_plus'],
            cantons_with_data=cantons_with_data,
            cantons_without_data=cantons_without_data
        )

    async def get_canton_summary_for_data_source(self, data_source_id: str) -> Optional[str]:
        """Get a compact canton summary for display in the main data source list.

        Returns:
        - None if all cantons have complete coverage (nothing to show)
        - String with incomplete/missing canton abbreviations for display
        """
        canton_info = await self.get_canton_availability_for_data_source(data_source_id)
        if not canton_info:
            return None

        # Collect cantons with incomplete coverage (<100%) or missing data
        incomplete_cantons = []
        missing_cantons = []

        # Check all cantons for incomplete coverage
        all_cantons = canton_info.cantons_with_data + canton_info.cantons_without_data

        for canton in all_cantons:
            if canton.coverage_percentage == 0:
                missing_cantons.append(canton.canton_abbreviation)
            elif canton.coverage_percentage < 100:
                incomplete_cantons.append(canton.canton_abbreviation)

        # Build summary string
        summary_parts = []
        if incomplete_cantons:
            summary_parts.append(f"Unvollständig: {', '.join(sorted(incomplete_cantons))}")
        if missing_cantons:
            summary_parts.append(f"Fehlend: {', '.join(sorted(missing_cantons))}")

        # Return None if no incomplete/missing cantons (all have 100% coverage)
        if not summary_parts:
            return None

        return " | ".join(summary_parts)

    async def _get_all_cantons_available(self) -> DataSourceCantonInfo:
        """Get canton info where all cantons are available (for SwissBoundaries3D)."""
        cantons_with_data = []

        for canton_num, (abbr, name) in self.CANTON_MAPPING.items():
            # Get total municipalities for this canton
            query = """
                SELECT COUNT(*) as total_municipalities
                FROM ST_Read('raw_input_data/821838/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg', layer='tlm_hoheitsgebiet')
                WHERE kantonsnummer = ?
            """
            result = self.db.execute(query, [canton_num]).fetchone()
            total_municipalities = result[0] if result else 0

            if total_municipalities > 0:
                cantons_with_data.append(CantonAvailability(
                    canton_abbreviation=abbr,
                    canton_name=name,
                    municipalities_with_data=total_municipalities,
                    total_municipalities=total_municipalities,
                    coverage_percentage=100.0
                ))

        return DataSourceCantonInfo(
            total_cantons=len(cantons_with_data),
            cantons_with_50_percent_plus=len(cantons_with_data),
            cantons_with_data=cantons_with_data,
            cantons_without_data=[]
        )

    async def _calculate_canton_availability(self, data_config: Dict) -> DataSourceCantonInfo:
        """Calculate canton availability for a specific data source."""
        # Get canton totals and coverage in one query
        query = """
            WITH canton_totals AS (
                SELECT
                    c.name as canton_name,
                    c.kantonsnummer,
                    COUNT(m.name) as total_municipalities
                FROM ST_Read('raw_input_data/821838/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg', layer='tlm_kantonsgebiet') c
                JOIN ST_Read('raw_input_data/821838/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg', layer='tlm_hoheitsgebiet') m
                ON c.kantonsnummer = m.kantonsnummer
                GROUP BY c.name, c.kantonsnummer
            ),
            canton_with_data AS (
                SELECT
                    c.name as canton_name,
                    c.kantonsnummer,
                    COUNT(DISTINCT m.name) as municipalities_with_data
                FROM ST_Read(?, layer=?) zones
                JOIN ST_Read('raw_input_data/821838/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg', layer='tlm_hoheitsgebiet') m
                ON ST_Intersects(zones.geom, m.geom)
                JOIN ST_Read('raw_input_data/821838/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg', layer='tlm_kantonsgebiet') c
                ON m.kantonsnummer = c.kantonsnummer
                GROUP BY c.name, c.kantonsnummer
            )
            SELECT
                ct.canton_name,
                ct.kantonsnummer,
                COALESCE(cwd.municipalities_with_data, 0) as municipalities_with_data,
                ct.total_municipalities,
                ROUND(COALESCE(cwd.municipalities_with_data, 0) * 100.0 / ct.total_municipalities, 1) as coverage_percentage
            FROM canton_totals ct
            LEFT JOIN canton_with_data cwd ON ct.kantonsnummer = cwd.kantonsnummer
            ORDER BY coverage_percentage DESC
        """

        results = self.db.execute(query, [data_config["path"], data_config["layer"]]).fetchall()

        cantons_with_data = []
        cantons_without_data = []
        cantons_with_50_percent_plus = 0

        for row in results:
            canton_name, canton_num, municipalities_with_data, total_municipalities, coverage_percentage = row

            abbr = self.get_canton_abbreviation(canton_num)

            canton_availability = CantonAvailability(
                canton_abbreviation=abbr,
                canton_name=canton_name,
                municipalities_with_data=municipalities_with_data,
                total_municipalities=total_municipalities,
                coverage_percentage=coverage_percentage
            )

            if coverage_percentage >= 50.0:
                cantons_with_50_percent_plus += 1
                cantons_with_data.append(canton_availability)
            else:
                cantons_without_data.append(canton_availability)

        return DataSourceCantonInfo(
            total_cantons=len(results),
            cantons_with_50_percent_plus=cantons_with_50_percent_plus,
            cantons_with_data=cantons_with_data,
            cantons_without_data=cantons_without_data
        )
