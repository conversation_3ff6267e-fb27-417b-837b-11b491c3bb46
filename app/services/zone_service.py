"""
Zone service for business logic.
"""

import json
from typing import List, Optional, Dict, Any
from fastapi import Depends

from app.core.database import get_db_dependency
from app.core.config import settings
from app.models.zone import Zone, ZoneType

class ZoneService:
    """Service for zone-related operations."""

    def __init__(self, db = Depends(get_db_dependency)):
        self.db = db

    async def get_zones(
        self,
        municipality_id: Optional[str] = None,
        zone_type: Optional[ZoneType] = None,
        limit: int = 100
    ) -> List[Zone]:
        """Get zones with optional filtering."""
        try:
            # Return empty list if no municipality is specified
            # This application is designed for municipality-specific zone queries
            if not municipality_id or not municipality_id.strip():
                return []
            # Build query based on zone type with municipality filtering
            if zone_type == ZoneType.GRUNDNUTZUNG or zone_type is None:
                # Query NPL grundnutzung data with municipality filter
                query = f"""
                    SELECT
                        hash(zones.typ_kommunal_bezeichnung || '_' || zones.typ_kantonal_bezeichnung || '_' || zones.kanton || '_' || COALESCE(zones.typ_kommunal_code, '') || '_' || ST_AsText(ST_Centroid(zones.geom))) as id,
                        'grundnutzung' as zone_type,
                        zones.typ_kommunal_code as municipality_code,
                        zones.typ_kommunal_bezeichnung as zone_name,
                        zones.typ_kantonal_bezeichnung as kantonal_zone_name,
                        zones.kanton,
                        ST_Area(zones.geom) as area
                    FROM npl_grundnutzung zones
                    JOIN swiss_municipalities municipalities
                    ON ST_Intersects(zones.geom, municipalities.geometry)
                    WHERE municipalities.tlm_hoheitsgebiet_name = ?
                    AND zones.geom IS NOT NULL
                    AND (ST_Area(ST_Intersection(zones.geom, municipalities.geometry)) / ST_Area(zones.geom)) >= {settings.spatial_filter_threshold}
                    ORDER BY zones.typ_kommunal_bezeichnung LIMIT {limit}
                """

                result = self.db.execute(query, [municipality_id]).fetchall()

                zones = []
                for row in result:
                    zones.append(Zone(
                        id=str(row[0]),
                        zone_type=ZoneType.GRUNDNUTZUNG,
                        municipality_id=row[2] if row[2] else municipality_id,
                        zone_name=row[3],
                        description=row[4],  # kantonal zone name as description
                        area=row[6] if row[6] else None,
                        properties={
                            "canton": row[5],
                            "kantonal_zone_name": row[4]
                        }
                    ))

                return zones

            # Handle other zone types
            elif zone_type == ZoneType.UEBERLAGERNDE_FLAECHEN:
                # Query NPL ueberlagernde_flaechen data with municipality filter
                query = f"""
                    SELECT
                        hash(zones.typ_kommunal_bezeichnung || '_' || zones.typ_kantonal_bezeichnung || '_' || zones.kanton || '_' || COALESCE(zones.typ_kommunal_code, '') || '_' || ST_AsText(ST_Centroid(zones.geom))) as id,
                        'ueberlagernde_flaechen' as zone_type,
                        zones.typ_kommunal_code as municipality_code,
                        zones.typ_kommunal_bezeichnung as zone_name,
                        zones.typ_kantonal_bezeichnung as kantonal_zone_name,
                        zones.kanton,
                        ST_Area(zones.geom) as area
                    FROM npl_ueberlagernde_flaechen zones
                    JOIN swiss_municipalities municipalities
                    ON ST_Intersects(zones.geom, municipalities.geometry)
                    WHERE municipalities.tlm_hoheitsgebiet_name = ?
                    AND zones.geom IS NOT NULL
                    AND (ST_Area(ST_Intersection(zones.geom, municipalities.geometry)) / ST_Area(zones.geom)) >= {settings.spatial_filter_threshold}
                    ORDER BY zones.typ_kommunal_bezeichnung LIMIT {limit}
                """

                result = self.db.execute(query, [municipality_id]).fetchall()

                zones = []
                for row in result:
                    zones.append(Zone(
                        id=str(row[0]),
                        zone_type=ZoneType.UEBERLAGERNDE_FLAECHEN,
                        municipality_id=row[2] if row[2] else municipality_id,
                        zone_name=row[3],
                        description=row[4],
                        area=row[6] if row[6] else None,
                        properties={
                            "canton": row[5],
                            "kantonal_zone_name": row[4]
                        }
                    ))

                return zones

            return []

        except Exception as e:
            raise Exception(f"Error fetching zones: {str(e)}")

    async def get_zone_by_id(self, zone_id: str) -> Optional[Zone]:
        """Get zone by ID."""
        try:
            # Zone IDs are now generated using hash of zone properties
            # Try to get zone from grundnutzung table first
            # Transform coordinates from Swiss LV95 (EPSG:2056) to WGS84 (EPSG:4326) for web mapping
            query = """
                SELECT
                    hash(typ_kommunal_bezeichnung || '_' || typ_kantonal_bezeichnung || '_' || kanton || '_' || COALESCE(typ_kommunal_code, '') || '_' || ST_AsText(ST_Centroid(geom))) as id,
                    'grundnutzung' as zone_type,
                    typ_kommunal_code as municipality_code,
                    typ_kommunal_bezeichnung as zone_name,
                    typ_kantonal_bezeichnung as kantonal_zone_name,
                    kanton,
                    ST_Area(geom) as area,
                    ST_AsGeoJSON(ST_FlipCoordinates(ST_Transform(geom, 'EPSG:2056', 'EPSG:4326'))) as geometry
                FROM npl_grundnutzung
                WHERE geom IS NOT NULL
                AND hash(typ_kommunal_bezeichnung || '_' || typ_kantonal_bezeichnung || '_' || kanton || '_' || COALESCE(typ_kommunal_code, '') || '_' || ST_AsText(ST_Centroid(geom))) = ?
            """

            result = self.db.execute(query, [int(zone_id)]).fetchone()

            if result:
                return Zone(
                    id=str(result[0]),
                    zone_type=ZoneType.GRUNDNUTZUNG,
                    municipality_id=result[2],
                    zone_name=result[3],
                    description=result[4],
                    area=result[6] if result[6] else None,
                    properties={
                        "canton": result[5],
                        "kantonal_zone_name": result[4],
                        "geometry": result[7] if result[7] else None
                    }
                )

            # If not found in grundnutzung, try ueberlagernde_flaechen
            # Transform coordinates from Swiss LV95 (EPSG:2056) to WGS84 (EPSG:4326) for web mapping
            query = """
                SELECT
                    hash(typ_kommunal_bezeichnung || '_' || typ_kantonal_bezeichnung || '_' || kanton || '_' || COALESCE(typ_kommunal_code, '') || '_' || ST_AsText(ST_Centroid(geom))) as id,
                    'ueberlagernde_flaechen' as zone_type,
                    typ_kommunal_code as municipality_code,
                    typ_kommunal_bezeichnung as zone_name,
                    typ_kantonal_bezeichnung as kantonal_zone_name,
                    kanton,
                    ST_Area(geom) as area,
                    ST_AsGeoJSON(ST_FlipCoordinates(ST_Transform(geom, 'EPSG:2056', 'EPSG:4326'))) as geometry
                FROM npl_ueberlagernde_flaechen
                WHERE geom IS NOT NULL
                AND hash(typ_kommunal_bezeichnung || '_' || typ_kantonal_bezeichnung || '_' || kanton || '_' || COALESCE(typ_kommunal_code, '') || '_' || ST_AsText(ST_Centroid(geom))) = ?
            """

            result = self.db.execute(query, [int(zone_id)]).fetchone()

            if result:
                return Zone(
                    id=str(result[0]),
                    zone_type=ZoneType.UEBERLAGERNDE_FLAECHEN,
                    municipality_id=result[2],
                    zone_name=result[3],
                    description=result[4],
                    area=result[6] if result[6] else None,
                    properties={
                        "canton": result[5],
                        "kantonal_zone_name": result[4],
                        "geometry": result[7] if result[7] else None
                    }
                )

            return None

        except (ValueError, Exception) as e:
            # If zone_id is not a valid integer or other error
            return None

    async def get_municipality_zones_geojson(
        self,
        municipality_id: str,
        zone_type: Optional[ZoneType] = None
    ) -> Dict[str, Any]:
        """Get zones for a municipality as GeoJSON."""
        try:
            # Build query based on zone type, default to grundnutzung
            if zone_type == ZoneType.GRUNDNUTZUNG or zone_type is None:
                table_name = "npl_grundnutzung"
            elif zone_type == ZoneType.UEBERLAGERNDE_FLAECHEN:
                table_name = "npl_ueberlagernde_flaechen"
            elif zone_type == ZoneType.UEBERLAGERNDE_LINIEN:
                table_name = "npl_ueberlagernde_linien"
            elif zone_type == ZoneType.UEBERLAGERNDE_PUNKTE:
                table_name = "npl_ueberlagernde_punkte"
            else:
                table_name = "npl_grundnutzung"

            # Use spatial filtering to find zones within municipality
            # Transform coordinates from Swiss LV95 (EPSG:2056) to WGS84 (EPSG:4326) for web mapping
            # Use the same hash-based ID generation as in get_zone_by_id for consistency
            query = f"""
                SELECT
                    hash(zones.typ_kommunal_bezeichnung || '_' || zones.typ_kantonal_bezeichnung || '_' || zones.kanton || '_' || COALESCE(zones.typ_kommunal_code, '') || '_' || ST_AsText(ST_Centroid(zones.geom))) as id,
                    zones.typ_kommunal_bezeichnung as zone_name,
                    zones.typ_kantonal_bezeichnung as kantonal_zone_name,
                    zones.typ_kommunal_code as municipality_code,
                    zones.kanton,
                    ST_AsGeoJSON(ST_FlipCoordinates(ST_Transform(zones.geom, 'EPSG:2056', 'EPSG:4326'))) as geometry,
                    ST_Area(zones.geom) as area,
                    ROUND((ST_Area(ST_Intersection(zones.geom, municipalities.geometry)) / ST_Area(zones.geom)) * 100, 1) as percentage_within
                FROM {table_name} zones
                JOIN swiss_municipalities municipalities
                ON ST_Intersects(zones.geom, municipalities.geometry)
                WHERE municipalities.tlm_hoheitsgebiet_name = ?
                AND zones.geom IS NOT NULL
                AND (ST_Area(ST_Intersection(zones.geom, municipalities.geometry)) / ST_Area(zones.geom)) >= {settings.spatial_filter_threshold}
                ORDER BY zones.typ_kommunal_bezeichnung
            """

            result = self.db.execute(query, [municipality_id]).fetchall()

            features = []
            for row in result:
                geometry = json.loads(row[5]) if row[5] else None
                if geometry:
                    # Handle NaN values that can cause JSON serialization errors
                    area = row[6] if row[6] is not None and not (isinstance(row[6], float) and (row[6] != row[6])) else 0
                    percentage = row[7] if row[7] is not None and not (isinstance(row[7], float) and (row[7] != row[7])) else 0

                    features.append({
                        "type": "Feature",
                        "properties": {
                            "id": str(row[0]),  # Convert hash to string for consistency
                            "zone_name": row[1],
                            "kantonal_zone_name": row[2],
                            "municipality_code": row[3],
                            "canton": row[4],
                            "area": area,
                            "percentage_within": percentage,
                            "zone_type": zone_type.value if zone_type else "grundnutzung"
                        },
                        "geometry": geometry
                    })

            return {
                "type": "FeatureCollection",
                "features": features,
                "properties": {
                    "municipality": municipality_id,
                    "zone_type": zone_type.value if zone_type else "grundnutzung",
                    "feature_count": len(features)
                }
            }

        except Exception as e:
            raise Exception(f"Error fetching municipality zones GeoJSON: {str(e)}")


