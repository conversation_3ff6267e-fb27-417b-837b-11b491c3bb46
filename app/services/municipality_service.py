"""
Municipality service for business logic.
"""

import json
import os
from typing import List, Optional, Dict, Any
import duckdb

from app.models.municipality import Municipality, MunicipalityBounds

class MunicipalityService:
    """Service for municipality-related operations."""

    def __init__(self, db: duckdb.DuckDBPyConnection):
        self.db = db
        self._municipality_cache = {}  # Cache for expensive spatial queries
        self._cache_file = "data/cache/municipality_availability.json"
        self._load_cache()

    async def get_municipalities(self, canton: Optional[str] = None, data_source_id: Optional[str] = None) -> List[Municipality]:
        """Get list of municipalities, optionally filtered by canton and data availability."""
        try:
            # First check if swiss_municipalities table exists
            if not await self._table_exists("swiss_municipalities"):
                # Return empty list if table doesn't exist - UI will handle this
                return []

            query = """
                SELECT DISTINCT
                    tlm_hoheitsgebiet_name as name,
                    tlm_hoheitsgebiet_name as id,
                    'CH' as canton
                FROM swiss_municipalities
                WHERE tlm_hoheitsgebiet_name IS NOT NULL
            """

            if canton:
                query += f" AND canton = '{canton}'"

            query += " ORDER BY name"

            result = self.db.execute(query).fetchall()

            municipalities = []
            for row in result:
                municipality = Municipality(
                    id=row[1],
                    name=row[0],
                    canton=row[2]
                )

                municipalities.append(municipality)

            # Check data availability in batch if data_source_id is provided
            if data_source_id:
                await self._update_municipalities_availability(municipalities, data_source_id)

            return municipalities

        except Exception as e:
            # Return empty list on error - UI will handle this gracefully
            return []

    async def get_municipality_by_id(self, municipality_id: str) -> Optional[Municipality]:
        """Get municipality by ID."""
        try:
            # Check if swiss_municipalities table exists
            if not await self._table_exists("swiss_municipalities"):
                # Return None if table doesn't exist
                return None

            query = """
                SELECT DISTINCT
                    tlm_hoheitsgebiet_name as name,
                    tlm_hoheitsgebiet_name as id,
                    'CH' as canton
                FROM swiss_municipalities
                WHERE tlm_hoheitsgebiet_name = ?
            """

            result = self.db.execute(query, [municipality_id]).fetchone()

            if not result:
                return None

            return Municipality(
                id=result[1],
                name=result[0],
                canton=result[2]
            )

        except Exception as e:
            # Return None on error
            return None

    async def get_municipality_bounds(self, municipality_id: str) -> Optional[MunicipalityBounds]:
        """Get geographic bounds for a municipality in WGS84 coordinates."""
        try:
            # Transform geometry to WGS84 (EPSG:4326) before getting bounds
            # Note: ST_Transform from EPSG:2056 to EPSG:4326 returns (lat, lon) so we need to swap
            query = """
                SELECT
                    ST_YMin(ST_Envelope(ST_Transform(geometry, 'EPSG:2056', 'EPSG:4326'))) as min_x,
                    ST_XMin(ST_Envelope(ST_Transform(geometry, 'EPSG:2056', 'EPSG:4326'))) as min_y,
                    ST_YMax(ST_Envelope(ST_Transform(geometry, 'EPSG:2056', 'EPSG:4326'))) as max_x,
                    ST_XMax(ST_Envelope(ST_Transform(geometry, 'EPSG:2056', 'EPSG:4326'))) as max_y
                FROM swiss_municipalities
                WHERE tlm_hoheitsgebiet_name = ?
            """

            result = self.db.execute(query, [municipality_id]).fetchone()

            if not result:
                # Fallback: try without transformation if the above fails
                fallback_query = """
                    SELECT
                        ST_XMin(ST_Envelope(geometry)) as min_x,
                        ST_YMin(ST_Envelope(geometry)) as min_y,
                        ST_XMax(ST_Envelope(geometry)) as max_x,
                        ST_YMax(ST_Envelope(geometry)) as max_y
                    FROM swiss_municipalities
                    WHERE tlm_hoheitsgebiet_name = ?
                """
                result = self.db.execute(fallback_query, [municipality_id]).fetchone()

                if not result:
                    return None

            return MunicipalityBounds(
                min_x=result[0],
                min_y=result[1],
                max_x=result[2],
                max_y=result[3]
            )

        except Exception as e:
            raise Exception(f"Error fetching municipality bounds: {str(e)}")

    async def _update_municipalities_availability(self, municipalities: List[Municipality], data_source_id: str):
        """Update availability for all municipalities in batch for better performance."""
        try:
            # For SwissBoundaries3D, all municipalities are available
            if data_source_id in ["821838", "swissboundaries"]:
                for municipality in municipalities:
                    municipality.has_data = True
                return

            # For other data sources, get list of municipalities with data in one query
            municipalities_with_data = await self._get_municipalities_with_data(data_source_id)

            # Update availability based on the result
            for municipality in municipalities:
                municipality.has_data = municipality.name in municipalities_with_data

        except Exception:
            # If batch checking fails, assume all are available to avoid blocking UI
            for municipality in municipalities:
                municipality.has_data = True

    async def _get_municipalities_with_data(self, data_source_id: str) -> set:
        """Get set of municipality names that have data for the given data source using optimized spatial queries."""
        try:
            # Check cache first to avoid expensive re-computation
            if data_source_id in self._municipality_cache:
                return self._municipality_cache[data_source_id]

            if data_source_id in ["820737", "nutzungsplanung"]:
                # Optimize DuckDB performance
                self.db.execute("SET threads TO 8")

                # Geodienste Nutzungsplanung - use GPKG data directly
                query = """
                    SELECT DISTINCT muni.tlm_hoheitsgebiet_name
                    FROM npl_grundnutzung geodienste
                    JOIN swiss_municipalities muni
                        ON ST_Intersects(geodienste.geom, muni.geometry)
                    WHERE muni.tlm_hoheitsgebiet_name IS NOT NULL
                """

                result = self.db.execute(query).fetchall()
                municipalities_with_data = {row[0] for row in result if row[0]}

                # Cache the result for future requests
                self._municipality_cache[data_source_id] = municipalities_with_data
                self._save_cache()

                return municipalities_with_data

            # For unknown data sources, return empty set (no data available)
            return set()

        except Exception:
            return set()  # Return empty set on error

    async def _check_municipality_data_availability(self, municipality_name: str, data_source_id: str) -> bool:
        """Check if a municipality has data available in the specified data source using spatial intersection."""
        try:
            # Map data source IDs to their corresponding data files/tables
            data_source_mapping = {
                "820737": {  # Geodienste Nutzungsplanung
                    "type": "parquet",
                    "path": "data/processed/npl_files/grundnutzung.parquet",
                    "geometry_column": "geom"
                },
                "821838": {  # SwissBoundaries3D
                    "type": "gpkg",
                    "path": "raw_input_data/821838/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg",
                    "geometry_column": "geometry"
                },
                # Legacy support
                "nutzungsplanung": {
                    "type": "parquet",
                    "path": "data/processed/npl_files/grundnutzung.parquet",
                    "geometry_column": "geom"
                },
                "swissboundaries": {
                    "type": "gpkg",
                    "path": "raw_input_data/821838/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg",
                    "geometry_column": "geometry"
                }
            }

            data_config = data_source_mapping.get(data_source_id)
            if not data_config:
                return True  # Unknown data source, assume available

            # For SwissBoundaries3D, all municipalities are available (it's the boundary data itself)
            if data_source_id in ["821838", "swissboundaries"]:
                return True

            # For other data sources, check spatial intersection
            return await self._check_spatial_intersection(municipality_name, data_config)

        except Exception:
            # If there's an error checking availability, assume it's available
            # to avoid blocking the UI
            return True

    def _load_cache(self):
        """Load municipality availability cache from file."""
        try:
            if os.path.exists(self._cache_file):
                with open(self._cache_file, 'r') as f:
                    cache_data = json.load(f)
                    # Convert lists back to sets
                    self._municipality_cache = {
                        k: set(v) for k, v in cache_data.items()
                    }
        except Exception:
            # If cache loading fails, start with empty cache
            self._municipality_cache = {}

    def _save_cache(self):
        """Save municipality availability cache to file."""
        try:
            os.makedirs(os.path.dirname(self._cache_file), exist_ok=True)
            cache_data = {
                k: list(v) for k, v in self._municipality_cache.items()
            }
            with open(self._cache_file, 'w') as f:
                json.dump(cache_data, f)
        except Exception:
            # If cache saving fails, continue without caching
            pass

    async def _check_spatial_intersection(self, municipality_name: str, data_config: Dict[str, Any]) -> bool:
        """Check if data spatially intersects with municipality boundaries using DuckDB spatial functions."""
        try:
            # Use a more efficient approach: check if any data exists within municipality bounds
            # This uses the existing swiss_municipalities view for better performance

            if data_config["type"] == "parquet":
                # For Parquet files (like NPL data) - use bounding box intersection first for speed
                intersection_query = f"""
                    SELECT 1
                    FROM read_parquet('{data_config["path"]}') data
                    JOIN swiss_municipalities muni
                        ON muni.tlm_hoheitsgebiet_name = ?
                    WHERE ST_Intersects(
                        ST_GeomFromWKB(data.{data_config["geometry_column"]}),
                        muni.geometry
                    )
                    LIMIT 1
                """
            else:
                # For GPKG files
                intersection_query = f"""
                    SELECT 1
                    FROM ST_Read('{data_config["path"]}') data
                    JOIN swiss_municipalities muni
                        ON muni.tlm_hoheitsgebiet_name = ?
                    WHERE ST_Intersects(
                        data.{data_config["geometry_column"]},
                        muni.geometry
                    )
                    LIMIT 1
                """

            result = self.db.execute(intersection_query, [municipality_name]).fetchone()
            return result is not None

        except Exception:
            # If spatial query fails, fall back to conservative approach
            return True  # Assume available to avoid blocking UI

    async def _table_exists(self, table_name: str) -> bool:
        """Check if a table or view exists in the database."""
        try:
            # Try to query the table with LIMIT 0 to check existence without data
            self.db.execute(f"SELECT * FROM {table_name} LIMIT 0")
            return True
        except Exception:
            return False


