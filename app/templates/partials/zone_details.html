<!-- Zone details partial for modal display -->
{% if zone %}
<div class="zone-details-content">
    <div class="zone-header">
        <h4>{{ zone.zone_name or "Unnamed Zone" }}</h4>
        <span class="zone-type-badge zone-type-{{ zone.zone_type.value }}">{{ zone.zone_type.value }}</span>
    </div>

    <div class="zone-properties">
        <div class="property-grid">
            <div class="property">
                <label>Zone Type:</label>
                <span>{{ zone.zone_type.value|title }}</span>
            </div>

            {% if zone.municipality_id %}
            <div class="property">
                <label>Municipality Code:</label>
                <span>{{ zone.municipality_id }}</span>
            </div>
            {% endif %}

            {% if zone.zone_code %}
            <div class="property">
                <label>Zone Code:</label>
                <span>{{ zone.zone_code }}</span>
            </div>
            {% endif %}

            {% if zone.area %}
            <div class="property">
                <label>Area:</label>
                <span class="area-value">{{ "{:.0f}".format(zone.area) }} m²</span>
            </div>
            {% endif %}

            {% if zone.description %}
            <div class="property">
                <label>Description:</label>
                <span>{{ zone.description }}</span>
            </div>
            {% endif %}
        </div>

        {% if zone.properties %}
        <div class="additional-properties">
            <h5>Additional Properties:</h5>
            <div class="property-grid">
                {% for key, value in zone.properties.items() %}
                {% if key != 'geometry' %}
                <div class="property">
                    <label>{{ key|title }}:</label>
                    <span>{{ value }}</span>
                </div>
                {% endif %}
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>

    <div class="zone-actions">
        <button type="button" class="btn-small" onclick="closeModal()">Close</button>
        {% if zone.properties and zone.properties.geometry %}
        <button type="button" class="btn-small btn-secondary" onclick="zoomToZone('{{ zone.id }}')">Zoom to Zone</button>
        {% endif %}
    </div>
</div>
{% else %}
<div class="zone-details-error">
    <p>Zone details could not be loaded.</p>
    <button type="button" class="btn-small" onclick="closeModal()">Close</button>
</div>
{% endif %}
