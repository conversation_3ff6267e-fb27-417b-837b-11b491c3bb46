<!-- Data Source Information -->
<div class="data-source-info">
    {% if data_source.legislative %}
    <div class="legislative-info">
        <h4>{{ data_source.display_name }}</h4>

        <div class="info-section">
            <h5>Beschreibung</h5>
            <p>{{ data_source.description or "Keine Beschreibung verfügbar" }}</p>
        </div>

        {% if data_source.legislative.technicalEntries %}
        {% set tech_entry = data_source.legislative.technicalEntries[0] %}

        <div class="info-section">
            <h5>Technische Details</h5>
            <div class="detail-grid">
                <div class="detail-item">
                    <strong>Technischer Datensatz:</strong> {{ tech_entry.label }}
                </div>
                <div class="detail-item">
                    <strong>Technische ID:</strong> {{ tech_entry.id }}
                </div>
                <div class="detail-item">
                    <strong>Identifier:</strong> {{ data_source.legislative.identifier }}
                </div>
                {% if tech_entry.status %}
                <div class="detail-item">
                    <strong>Status:</strong> {{ tech_entry.status }}
                </div>
                {% endif %}
                {% if tech_entry.geoCategory %}
                <div class="detail-item">
                    <strong>Kategorie:</strong> {{ tech_entry.geoCategory.name }}
                </div>
                <div class="detail-item">
                    <strong>Kategorie-Code:</strong> {{ tech_entry.geoCategory.code }}
                </div>
                {% endif %}
                <div class="detail-item">
                    <strong>Zugang:</strong> {{ data_source.legislative.access or "Nicht angegeben" }}
                </div>
                <div class="detail-item">
                    <strong>Download Service:</strong>
                    {% if data_source.legislative.downloadService %}Ja{% else %}Nein{% endif %}
                </div>
                <div class="detail-item">
                    <strong>ÖREB:</strong>
                    {% if data_source.legislative.oereb %}Ja{% else %}Nein{% endif %}
                </div>
                {% if tech_entry.startDate %}
                <div class="detail-item">
                    <strong>Startdatum:</strong> {{ tech_entry.startDate }}
                </div>
                {% endif %}
                {% if tech_entry.endDate %}
                <div class="detail-item">
                    <strong>Enddatum:</strong> {{ tech_entry.endDate }}
                </div>
                {% endif %}
                {% if tech_entry.hideLabel %}
                <div class="detail-item">
                    <strong>Label versteckt:</strong> Ja
                </div>
                {% endif %}
                {% if tech_entry.isHistorised %}
                <div class="detail-item">
                    <strong>Historisiert:</strong> Ja
                </div>
                {% endif %}
            </div>
        </div>

        {% if tech_entry.dataOwner or tech_entry.dataProvider or tech_entry.contactPerson or tech_entry.contactEmail %}
        <div class="info-section">
            <h5>Kontakt und Verantwortlichkeit</h5>
            <div class="detail-grid">
                {% if tech_entry.dataOwner %}
                <div class="detail-item">
                    <strong>Datenherr:</strong> {{ tech_entry.dataOwner }}
                </div>
                {% endif %}
                {% if tech_entry.dataProvider %}
                <div class="detail-item">
                    <strong>Datenanbieter:</strong> {{ tech_entry.dataProvider }}
                </div>
                {% endif %}
                {% if tech_entry.contactPerson %}
                <div class="detail-item">
                    <strong>Kontaktperson:</strong> {{ tech_entry.contactPerson }}
                </div>
                {% endif %}
                {% if tech_entry.contactEmail %}
                <div class="detail-item">
                    <strong>E-Mail:</strong>
                    <a href="mailto:{{ tech_entry.contactEmail }}">{{ tech_entry.contactEmail }}</a>
                </div>
                {% endif %}
                {% if tech_entry.contactPhone %}
                <div class="detail-item">
                    <strong>Telefon:</strong> {{ tech_entry.contactPhone }}
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        {% if tech_entry.updateFrequency or tech_entry.spatialExtent or tech_entry.temporalExtent %}
        <div class="info-section">
            <h5>Datencharakteristika</h5>
            <div class="detail-grid">
                {% if tech_entry.updateFrequency %}
                <div class="detail-item">
                    <strong>Aktualisierungsfrequenz:</strong> {{ tech_entry.updateFrequency }}
                </div>
                {% endif %}
                {% if tech_entry.spatialExtent %}
                <div class="detail-item">
                    <strong>Räumliche Ausdehnung:</strong> {{ tech_entry.spatialExtent }}
                </div>
                {% endif %}
                {% if tech_entry.temporalExtent %}
                <div class="detail-item">
                    <strong>Zeitliche Ausdehnung:</strong> {{ tech_entry.temporalExtent }}
                </div>
                {% endif %}
                {% if tech_entry.resolution %}
                <div class="detail-item">
                    <strong>Auflösung:</strong> {{ tech_entry.resolution }}
                </div>
                {% endif %}
                {% if tech_entry.accuracy %}
                <div class="detail-item">
                    <strong>Genauigkeit:</strong> {{ tech_entry.accuracy }}
                </div>
                {% endif %}
                {% if tech_entry.lineage %}
                <div class="detail-item">
                    <strong>Datenherkunft:</strong> {{ tech_entry.lineage }}
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        {% if tech_entry.dataModelUrl or tech_entry.modelDocumentationUrl or tech_entry.metadataUrls %}
        <div class="info-section">
            <h5>Links und Dokumentation</h5>
            <div class="links-list">
                {% if tech_entry.dataModelUrl %}
                <div class="link-item">
                    <strong>Datenmodell:</strong>
                    <a href="{{ tech_entry.dataModelUrl }}" target="_blank" rel="noopener">
                        {{ tech_entry.dataModelUrl }}
                    </a>
                </div>
                {% endif %}

                {% if tech_entry.modelDocumentationUrl %}
                <div class="link-item">
                    <strong>Modelldokumentation:</strong>
                    <a href="{{ tech_entry.modelDocumentationUrl }}" target="_blank" rel="noopener">
                        {{ tech_entry.modelDocumentationUrl }}
                    </a>
                </div>
                {% endif %}

                {% for metadata_url in tech_entry.metadataUrls %}
                <div class="link-item">
                    <strong>{{ metadata_url.urlLabel or "Metadaten" }}:</strong>
                    <a href="{{ metadata_url.url }}" target="_blank" rel="noopener">
                        {{ metadata_url.url }}
                    </a>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        {% endif %}
    </div>
    {% endif %}

    {% if data_source.local %}
    <div class="local-info">
        <div class="info-section">
            <h5>Lokale Verfügbarkeit</h5>
            <div class="detail-grid">
                <div class="detail-item">
                    <strong>Status:</strong>
                    <span class="status-badge {{ data_source.local.status }}">
                        {% if data_source.local.status == 'available' %}Verfügbar{% else %}{{ data_source.local.status }}{% endif %}
                    </span>
                </div>
                {% if data_source.local.last_updated %}
                <div class="detail-item">
                    <strong>Letzte Aktualisierung:</strong> {{ data_source.local.last_updated }}
                </div>
                {% endif %}
            </div>
        </div>

        {% if data_source.local.file_paths %}
        <div class="info-section">
            <h5>Lokale Dateien</h5>
            <ul class="file-list">
                {% for file_path in data_source.local.file_paths %}
                <li><code>{{ file_path }}</code></li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        {% if data_source.local.metadata %}
        <div class="info-section">
            <h5>Metadaten</h5>
            <div class="detail-grid">
                {% for key, value in data_source.local.metadata.items() %}
                <div class="detail-item">
                    <strong>{{ key }}:</strong> {{ value }}
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
    {% endif %}

    {% if not data_source.is_available %}
    <div class="unavailable-notice">
        <h5>⚠️ Datenquelle nicht verfügbar</h5>
        <p>Diese Datenquelle ist in der offiziellen Schweizer Geodateninfrastruktur verfügbar, aber noch nicht lokal implementiert.</p>
        <p>Für die Integration dieser Datenquelle kontaktieren Sie bitte die Entwickler.</p>
    </div>
    {% endif %}
</div>

<style>
.data-source-info {
    max-height: 70vh;
    overflow-y: auto;
}

.info-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.info-section:last-child {
    border-bottom: none;
}

.info-section h5 {
    margin: 0 0 0.5rem 0;
    color: #333;
    font-size: 1rem;
    font-weight: 600;
}

.detail-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.5rem;
}

.detail-item {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.detail-item strong {
    min-width: 120px;
    color: #666;
}

.links-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.link-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.link-item a {
    color: #0066cc;
    text-decoration: none;
    word-break: break-all;
    font-size: 0.9rem;
}

.link-item a:hover {
    text-decoration: underline;
}

.file-list {
    margin: 0;
    padding-left: 1rem;
}

.file-list li {
    margin-bottom: 0.25rem;
}

.file-list code {
    background: #f5f5f5;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.85rem;
}

.status-badge {
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-badge.available {
    background: #d4edda;
    color: #155724;
}

.status-badge.unavailable {
    background: #f8d7da;
    color: #721c24;
}

.unavailable-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 1rem;
    margin-top: 1rem;
}

.unavailable-notice h5 {
    margin: 0 0 0.5rem 0;
    color: #856404;
}

.unavailable-notice p {
    margin: 0.5rem 0;
    color: #856404;
}


</style>
