{% extends "base.html" %}

{% block title %}Schweizer Zonendaten-Viewer{% endblock %}

{% block content %}
<div class="app-layout">
    <!-- Main content with sidebar layout -->
    <div class="main-content-with-sidebar">
        <!-- ÖREB Sidebar (Left) -->
        <div id="oereb-sidebar" class="oereb-sidebar hidden">
            <div class="oereb-sidebar-header">
                <h3>ÖREB-Informationen</h3>
                <button type="button" class="oereb-close-button" onclick="closeOEREBSidebar()">✕</button>
            </div>
            <div class="oereb-sidebar-content">
                <div id="oereb-sidebar-content">
                    <div class="oereb-welcome">
                        <p>Klicken Sie auf die Karte, um ÖREB-Informationen für ein Grundstück abzurufen.</p>
                        <p class="note">ÖREB = Öffentlich-rechtliche Eigentumsbeschränkungen</p>
                    </div>
                </div>
                <!-- ÖREB loading indicator -->
                <div id="oereb-loading-indicator" class="oereb-loading-indicator hidden">
                    <div class="loading-spinner"></div>
                    <span>Lade ÖREB-Daten...</span>
                </div>
            </div>
        </div>

        <!-- Main content area (map + controls) -->
        <div class="main-content-area">
            <!-- Map Section (Top) -->
            <div class="map-section">
                <div id="map"></div>
                <!-- Map loading indicator (centered on map) -->
                <div id="map-loading-indicator" class="map-loading-indicator hidden">
                    Loading...
                </div>
            </div>

            <!-- Combined Middle and Bottom Section with Single Scrollbar -->
            <div class="middle-bottom-container">
                <!-- Query Section (Middle) -->
                <div class="query-section">
                    <div class="query-controls">
                        <div class="data-source-selection">
                            <h3>Lokale Datenquellen</h3>
                            <div
                                hx-get="/api/data-sources/selector"
                                hx-trigger="load"
                                hx-target="#data-source-selector"
                            >
                                <div id="data-source-selector">
                                    Lade Datenquellen...
                                </div>
                            </div>
                        </div>

                        <div class="query-controls-row">
                            <div class="municipality-selection">
                                <div
                                    hx-get="/api/municipalities/selector"
                                    hx-trigger="load"
                                    hx-target="#municipality-selector"
                                >
                                    <div id="municipality-selector">
                                        Lade Gemeinden...
                                    </div>
                                </div>
                            </div>

                            <div class="zone-filters">
                                <div class="filter-group">
                                    <select id="zone-type" name="zone_type" title="Zonentyp zum Filtern auswählen">
                                        <option value="">Alle Typen</option>
                                        <option value="grundnutzung">Grundnutzung (Bauzonen)</option>
                                        <option value="ueberlagernde_flaechen">Überlagernde Flächen</option>
                                        <option value="ueberlagernde_linien">Überlagernde Linien</option>
                                        <option value="ueberlagernde_punkte">Überlagernde Punkte</option>
                                    </select>
                                </div>
                            </div>

                            <div class="query-status">
                                <div id="query-status-container">
                                    <div id="query-status-message">
                                        Wählen Sie eine Datenquelle aus, um zu beginnen
                                    </div>
                                    <button
                                        type="button"
                                        id="show-municipality-zones-btn"
                                        class="btn-primary municipality-zones-button hidden"
                                        hx-get="/api/zones/"
                                        hx-target="#zones-table"
                                        hx-include="#municipality-select, #zone-type"
                                        hx-indicator="#table-loading-indicator"
                                    >
                                        Gemeindezonen anzeigen
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Results Section (Bottom) -->
                <div class="results-section">
                    <div class="results-header">
                        <h3>4. Ergebnisse</h3>
                        <div class="results-summary" id="results-summary">
                            Keine Zonen geladen
                        </div>
                    </div>

                    <div class="results-content">
                        <div class="results-tabs">
                            <button type="button" class="tab-button active" onclick="showTab('table')">Tabellenansicht</button>
                            <button type="button" class="tab-button" onclick="showTab('summary')">Gemeinde-Zonenliste</button>
                        </div>

                        <div id="table-tab" class="tab-content active">
                            <div id="table-loading-indicator" class="htmx-indicator">Lädt...</div>
                            <div id="zones-table">
                                <p class="placeholder">Wählen Sie eine Gemeinde aus und klicken Sie auf "Gemeindezonen anzeigen", um Ergebnisse zu sehen</p>
                            </div>
                        </div>

                        <div id="summary-tab" class="tab-content">
                            <div id="zones-summary">
                                <p class="placeholder">Zonenzusammenfassung wird hier angezeigt</p>
                            </div>
                        </div>
                        </div>
                    </div>
                </div>
            </div>
</div>

<!-- Zone details modal -->
<div id="zone-details-modal" class="modal hidden">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Zonendetails</h3>
            <button type="button" class="close-button" onclick="closeModal()">&times;</button>
        </div>
        <div class="modal-body">
            <!-- Zone details will be loaded here -->
            <div id="zone-details-content">
                <div id="zone-details-loading" class="htmx-indicator zone-details-loading">
                    <div class="loading-spinner"></div>
                    <span>Lade Zonendetails...</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Tab management
    function showTab(tabName) {
        // Hide all tabs
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('active');
        });

        // Show selected tab
        document.getElementById(`${tabName}-tab`).classList.add('active');
        event.target.classList.add('active');
    }

    function closeModal() {
        // Try multiple ways to find the modal
        let modal = document.getElementById('zone-details-modal');

        if (!modal) {
            // Try finding by class
            modal = document.querySelector('.modal');
        }

        if (!modal) {
            // Try finding any modal that's not hidden
            modal = document.querySelector('.modal:not(.hidden)');
        }

        if (modal) {
            modal.classList.add('hidden');
        } else {
            console.error('Modal element not found in closeModal!');
        }
    }

    // Make functions available globally
    window.closeModal = closeModal;

    // Setup modal event handlers
    function setupModalHandlers() {
        const modal = document.getElementById('zone-details-modal');
        if (modal) {
            // Close modal when clicking on backdrop
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });
        }
    }

    // Handle zone type filter changes
    document.addEventListener('DOMContentLoaded', function() {
        // Setup modal handlers
        setupModalHandlers();

        const zoneTypeSelect = document.getElementById('zone-type');
        if (zoneTypeSelect) {
            zoneTypeSelect.addEventListener('change', function() {
                const municipalitySelect = document.getElementById('municipality-select');
                const selectedMunicipality = municipalitySelect ? municipalitySelect.value : null;
                const selectedZoneType = this.value;

                if (selectedMunicipality) {
                    // Show loading indicator
                    if (typeof window.showMapLoading === 'function') {
                        window.showMapLoading();
                    }

                    // Trigger HTMX event to update map
                    htmx.trigger('#map', 'load-zones', {
                        municipality: selectedMunicipality,
                        zoneType: selectedZoneType
                    });

                    // Also reload the zones table
                    htmx.ajax('GET', '/api/zones/', {
                        target: '#zones-table',
                        values: {
                            municipality: selectedMunicipality,
                            zone_type: selectedZoneType
                        }
                    });
                }
            });
        }
    });
</script>
{% endblock %}
