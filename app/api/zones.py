"""
Zone data API endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from fastapi.templating import Jinja2Templates
from typing import Optional

from app.services.zone_service import ZoneService
from app.models.zone import ZoneList, ZoneType

templates = Jinja2Templates(directory="app/templates")

router = APIRouter()

@router.get("/")
async def get_zones(
    request: Request,
    municipality: Optional[str] = Query(None, description="Filter by municipality"),
    zone_type: Optional[str] = Query(None, description="Filter by zone type"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of zones to return"),
    service: ZoneService = Depends()
):
    """Get zones, optionally filtered by municipality and type."""
    try:
        # Convert string zone_type to ZoneType enum
        zone_type_filter = None
        if zone_type and zone_type.strip():
            try:
                zone_type_filter = ZoneType(zone_type.strip())
            except ValueError:
                # Invalid zone type, use None (will default to grundnutzung)
                zone_type_filter = None

        zones = await service.get_zones(
            municipality_id=municipality,
            zone_type=zone_type_filter,
            limit=limit
        )

        # Check if this is an HTMX request (wants HTML response)
        if request.headers.get("HX-Request"):
            # Construct the API query URL for display
            api_query_params = []
            if municipality:
                api_query_params.append(f"municipality={municipality}")
            if zone_type_filter:
                api_query_params.append(f"zone_type={zone_type_filter}")
            if limit != 100:  # Only include if different from default
                api_query_params.append(f"limit={limit}")

            api_query_url = "/api/zones/"
            if api_query_params:
                api_query_url += "?" + "&".join(api_query_params)

            return templates.TemplateResponse(
                "partials/zones_table.html",
                {
                    "request": request,
                    "zones": zones,
                    "municipality_id": municipality,
                    "zone_type": zone_type,
                    "api_query_url": api_query_url
                }
            )

        # Otherwise return JSON
        return ZoneList(zones=zones)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{zone_id}")
async def get_zone(
    zone_id: str,
    request: Request,
    service: ZoneService = Depends()
):
    """Get details for a specific zone."""
    try:
        zone = await service.get_zone_by_id(zone_id)
        if not zone:
            raise HTTPException(status_code=404, detail="Zone not found")

        # Check if this is an HTMX request (wants HTML response)
        if request.headers.get("HX-Request"):
            return templates.TemplateResponse(
                "partials/zone_details.html",
                {
                    "request": request,
                    "zone": zone
                }
            )

        # Otherwise return JSON
        return zone
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/municipality/{municipality_id}/geojson")
async def get_municipality_zones_geojson(
    municipality_id: str,
    zone_type: Optional[ZoneType] = Query(None, description="Filter by zone type"),
    service: ZoneService = Depends()
):
    """Get zones for a municipality as GeoJSON for map display."""
    try:
        geojson = await service.get_municipality_zones_geojson(
            municipality_id=municipality_id,
            zone_type=zone_type
        )
        return geojson
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))



