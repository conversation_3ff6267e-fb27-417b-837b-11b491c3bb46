"""
Data source API endpoints.
"""

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPEx<PERSON>, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import <PERSON><PERSON>2Templates
import duckdb

from app.services.data_source_service import DataSourceService
from app.models.data_source import DataSourceList, DataSourceInfo
from app.core.database import get_db_dependency

templates = Jinja2Templates(directory="app/templates")

router = APIRouter()


def get_data_source_service(db: duckdb.DuckDBPyConnection = Depends(get_db_dependency)) -> DataSourceService:
    """Dependency to get DataSourceService with database connection."""
    return DataSourceService(db=db)


@router.get("/", response_model=DataSourceList)
async def get_data_sources(
    service: DataSourceService = Depends(get_data_source_service)
):
    """Get list of all data sources with availability information."""
    try:
        return await service.get_data_sources()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/selector", response_class=HTMLResponse)
async def get_data_source_selector(
    request: Request,
    service: DataSourceService = Depends(get_data_source_service)
):
    """Get data source selector HTML for HTMX."""
    try:
        data_sources = await service.get_data_sources()
        return templates.TemplateResponse(
            "partials/data_source_selector.html",
            {
                "request": request,
                "data_sources": data_sources
            }
        )
    except Exception as e:
        return f'<div class="error">Error loading data sources: {str(e)}</div>'


@router.get("/{source_id}", response_model=DataSourceInfo)
async def get_data_source(
    source_id: str,
    service: DataSourceService = Depends(get_data_source_service)
):
    """Get details for a specific data source."""
    try:
        data_source = await service.get_data_source_by_id(source_id)
        if not data_source:
            raise HTTPException(status_code=404, detail="Data source not found")
        return data_source
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{source_id}/info", response_class=HTMLResponse)
async def get_data_source_info(
    source_id: str,
    request: Request,
    service: DataSourceService = Depends(get_data_source_service)
):
    """Get detailed information about a data source for modal display."""
    try:
        data_source = await service.get_data_source_by_id(source_id)
        if not data_source:
            raise HTTPException(status_code=404, detail="Data source not found")

        return templates.TemplateResponse(
            "partials/data_source_info.html",
            {
                "request": request,
                "data_source": data_source
            }
        )
    except Exception as e:
        return f'<div class="error">Error loading data source info: {str(e)}</div>'
