"""
Map-related API endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException

from app.services.spatial_service import SpatialService

router = APIRouter()

@router.get("/search")
async def search_locations(
    query: str,
    service: SpatialService = Depends()
):
    """Search for locations (municipalities, addresses, etc.)."""
    try:
        results = await service.search_locations(query)
        return {"results": results}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
