"""
Municipality-related API endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, Request, Query, Form
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from typing import Optional
import duckdb

from app.services.municipality_service import MunicipalityService
from app.models.municipality import Municipality, MunicipalityList
from app.core.database import get_db_dependency

templates = Jinja2Templates(directory="app/templates")

router = APIRouter()


def get_municipality_service(db: duckdb.DuckDBPyConnection = Depends(get_db_dependency)) -> MunicipalityService:
    """Dependency to get MunicipalityService with database connection."""
    return MunicipalityService(db=db)

@router.get("/", response_model=MunicipalityList)
async def get_municipalities(
    canton: Optional[str] = None,
    data_source: Optional[str] = None,
    service: MunicipalityService = Depends(get_municipality_service)
):
    """Get list of municipalities, optionally filtered by canton and data source availability."""
    try:
        municipalities = await service.get_municipalities(canton=canton, data_source_id=data_source)
        return MunicipalityList(municipalities=municipalities)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/selector", response_class=HTMLResponse)
async def get_municipality_selector(
    request: Request,
    canton: Optional[str] = None,
    data_source: Optional[str] = Query(None, description="Data source ID(s), can be multiple"),
    service: MunicipalityService = Depends(get_municipality_service)
):
    """Get municipality selector HTML for HTMX."""
    try:
        # Handle multiple data sources - use the first one for availability checking
        primary_data_source = data_source.split(',')[0] if data_source else None

        municipalities = await service.get_municipalities(canton=canton, data_source_id=primary_data_source)
        return templates.TemplateResponse(
            "partials/municipality_selector.html",
            {
                "request": request,
                "municipalities": municipalities,
                "data_source": primary_data_source
            }
        )
    except Exception as e:
        return f'<div class="error">Error loading municipalities: {str(e)}</div>'

@router.get("/{municipality_id}", response_model=Municipality)
async def get_municipality(
    municipality_id: str,
    service: MunicipalityService = Depends(get_municipality_service)
):
    """Get details for a specific municipality."""
    try:
        municipality = await service.get_municipality_by_id(municipality_id)
        if not municipality:
            raise HTTPException(status_code=404, detail="Municipality not found")
        return municipality
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{municipality_id}/bounds")
async def get_municipality_bounds(
    municipality_id: str,
    service: MunicipalityService = Depends(get_municipality_service)
):
    """Get geographic bounds for a municipality."""
    try:
        bounds = await service.get_municipality_bounds(municipality_id)
        if not bounds:
            raise HTTPException(status_code=404, detail="Municipality not found")
        return bounds
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


