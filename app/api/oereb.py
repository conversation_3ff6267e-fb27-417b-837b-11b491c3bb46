"""
ÖREB (Öffentlich-rechtliche Eigentumsbeschränkungen) API endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, Request, Query
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from typing import Optional
import logging

from app.services.oereb_service import OEREBService
from app.config.oereb_config import CANTONAL_OEREB_SERVICES

logger = logging.getLogger(__name__)
templates = Jinja2Templates(directory="app/templates")

router = APIRouter()

def get_oereb_service() -> OEREBService:
    """Dependency to get OEREBService."""
    return OEREBService()

@router.get("/lookup")
@router.post("/lookup")
async def lookup_oereb_by_coordinates(
    request: Request,
    x: float = Query(..., description="X coordinate in EPSG:2056"),
    y: float = Query(..., description="Y coordinate in EPSG:2056"),
    service: OEREBService = Depends(get_oereb_service)
):
    """
    Lookup ÖREB data by coordinates.

    This endpoint:
    1. Gets EGRID from coordinates using maps.geo.admin.ch
    2. Fetches ÖREB data from the appropriate cantonal service
    3. Returns HTML response for HTMX integration
    """
    try:
        # Step 1: Get EGRID from coordinates
        property_info = await service.get_egrid_from_coordinates(x, y)

        if not property_info:
            # No property found at these coordinates
            if request.headers.get("HX-Request"):
                return templates.TemplateResponse(
                    "partials/oereb_error.html",
                    {
                        "request": request,
                        "error_type": "no_property",
                        "message": "Kein Grundstück an dieser Position gefunden.",
                        "coordinates": {"x": x, "y": y}
                    }
                )
            else:
                raise HTTPException(status_code=404, detail="Kein Grundstück an dieser Position gefunden")

        # Step 2: Get ÖREB data using EGRID and canton
        egrid = property_info['egrid']
        canton = property_info['canton']

        oereb_data = await service.get_oereb_data(egrid, canton)

        if not oereb_data:
            # No ÖREB data available for this property
            if request.headers.get("HX-Request"):
                return templates.TemplateResponse(
                    "partials/oereb_error.html",
                    {
                        "request": request,
                        "error_type": "no_oereb_data",
                        "message": f"Keine ÖREB-Daten für Grundstück {egrid} verfügbar.",
                        "property_info": property_info
                    }
                )
            else:
                raise HTTPException(status_code=404, detail=f"Keine ÖREB-Daten für Grundstück {egrid} verfügbar")

        # Step 3: Return successful response
        if request.headers.get("HX-Request"):
            # Construct the API query URL for display
            api_query_url = f"/api/oereb/lookup?x={x}&y={y}"

            # Get cantonal service URLs for all formats
            cantonal_urls = service.get_cantonal_urls(canton, egrid)

            return templates.TemplateResponse(
                "partials/oereb_data.html",
                {
                    "request": request,
                    "property_info": property_info,
                    "oereb_data": oereb_data,
                    "coordinates": {"x": x, "y": y},
                    "api_query_url": api_query_url,
                    "cantonal_urls": cantonal_urls,
                    "canton": canton
                }
            )
        else:
            # Return JSON for non-HTMX requests
            return {
                "property_info": property_info,
                "oereb_data": oereb_data,
                "coordinates": {"x": x, "y": y}
            }

    except HTTPException as e:
        # Handle HTTP exceptions for HTMX requests
        if request.headers.get("HX-Request"):
            # Determine error type based on status code
            if e.status_code == 408:
                error_type = "timeout"
            elif e.status_code == 502:
                error_type = "service_unavailable"
            elif e.status_code == 404:
                error_type = "no_oereb_data"
            else:
                error_type = "unexpected_error"

            return templates.TemplateResponse(
                "partials/oereb_error.html",
                {
                    "request": request,
                    "error_type": error_type,
                    "message": e.detail,
                    "coordinates": {"x": x, "y": y},
                    "property_info": property_info if 'property_info' in locals() else None
                }
            )
        else:
            # Re-raise for non-HTMX requests
            raise
    except Exception as e:
        logger.error(f"Unexpected error in ÖREB lookup: {e}")
        if request.headers.get("HX-Request"):
            return templates.TemplateResponse(
                "partials/oereb_error.html",
                {
                    "request": request,
                    "error_type": "unexpected_error",
                    "message": "Unerwarteter Fehler beim Abrufen der ÖREB-Daten.",
                    "coordinates": {"x": x, "y": y}
                }
            )
        else:
            raise HTTPException(status_code=500, detail="Unerwarteter Fehler beim Abrufen der ÖREB-Daten")

@router.get("/egrid/{egrid}")
async def get_oereb_by_egrid(
    request: Request,
    egrid: str,
    canton: Optional[str] = Query(None, description="Canton abbreviation (e.g., ZH, BE)"),
    service: OEREBService = Depends(get_oereb_service)
):
    """
    Get ÖREB data directly by EGRID.

    If canton is not provided, this endpoint will attempt to determine it
    from the EGRID format or return an error.
    """
    try:
        if not canton:
            # Try to determine canton from EGRID format
            # This is a simplified approach - in reality, EGRID format varies
            raise HTTPException(status_code=400, detail="Kanton muss angegeben werden")

        oereb_data = await service.get_oereb_data(egrid, canton.upper())

        if not oereb_data:
            if request.headers.get("HX-Request"):
                return templates.TemplateResponse(
                    "partials/oereb_error.html",
                    {
                        "request": request,
                        "error_type": "no_oereb_data",
                        "message": f"Keine ÖREB-Daten für Grundstück {egrid} verfügbar.",
                        "property_info": {"egrid": egrid, "canton": canton}
                    }
                )
            else:
                raise HTTPException(status_code=404, detail=f"Keine ÖREB-Daten für Grundstück {egrid} verfügbar")

        if request.headers.get("HX-Request"):
            # Construct the API query URL for display
            api_query_url = f"/api/oereb/egrid/{egrid}?canton={canton}"

            # Get cantonal service URLs for all formats
            cantonal_urls = service.get_cantonal_urls(canton.upper(), egrid)

            return templates.TemplateResponse(
                "partials/oereb_data.html",
                {
                    "request": request,
                    "property_info": {"egrid": egrid, "canton": canton},
                    "oereb_data": oereb_data,
                    "api_query_url": api_query_url,
                    "cantonal_urls": cantonal_urls,
                    "canton": canton
                }
            )
        else:
            return {
                "property_info": {"egrid": egrid, "canton": canton},
                "oereb_data": oereb_data
            }

    except HTTPException as e:
        # Handle HTTP exceptions for HTMX requests
        if request.headers.get("HX-Request"):
            # Determine error type based on status code
            if e.status_code == 408:
                error_type = "timeout"
            elif e.status_code == 502:
                error_type = "service_unavailable"
            elif e.status_code == 404:
                error_type = "no_oereb_data"
            else:
                error_type = "unexpected_error"

            return templates.TemplateResponse(
                "partials/oereb_error.html",
                {
                    "request": request,
                    "error_type": error_type,
                    "message": e.detail,
                    "property_info": {"egrid": egrid, "canton": canton} if 'canton' in locals() else {"egrid": egrid}
                }
            )
        else:
            # Re-raise for non-HTMX requests
            raise
    except Exception as e:
        logger.error(f"Unexpected error getting ÖREB data for EGRID {egrid}: {e}")
        if request.headers.get("HX-Request"):
            return templates.TemplateResponse(
                "partials/oereb_error.html",
                {
                    "request": request,
                    "error_type": "unexpected_error",
                    "message": "Unerwarteter Fehler beim Abrufen der ÖREB-Daten.",
                    "property_info": {"egrid": egrid, "canton": canton}
                }
            )
        else:
            raise HTTPException(status_code=500, detail="Unerwarteter Fehler beim Abrufen der ÖREB-Daten")

@router.get("/cantons")
async def get_available_cantons(service: OEREBService = Depends(get_oereb_service)):
    """Get list of cantons with available ÖREB services."""
    return {
        "cantons": list(CANTONAL_OEREB_SERVICES.keys()),
        "services": CANTONAL_OEREB_SERVICES
    }
