/**
 * OpenLayers integration for Municipality Zones Viewer
 */

let map;
let currentMunicipalityId = null;
let zoneLayers = {};
let selectedDataSource = null;
let currentBackgroundLayer = 'osm';
let swissLayer, osmLayer;
let currentPopup = null;
let highlightLayer = null;
let wmsLayers = {};

// Color generator for unique zone colors
function generateZoneColor(zoneId, zoneType) {
    // Define color palettes for different zone types
    const colorPalettes = {
        'grundnutzung': [
            '#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#7B68EE',
            '#20B2AA', '#FF6347', '#4682B4', '#32CD32', '#FF69B4',
            '#8A2BE2', '#00CED1', '#FF4500', '#2F4F4F', '#9ACD32'
        ],
        'ueberlagernde_flaechen': [
            '#A23B72', '#8B4513', '#2F4F4F', '#800080', '#B22222',
            '#4B0082', '#8B008B', '#9932CC', '#8B0000', '#556B2F',
            '#A0522D', '#2E8B57', '#800000', '#483D8B', '#6B8E23'
        ],
        'ueberlagernde_linien': [
            '#F18F01', '#FF8C00', '#FFA500', '#FFD700', '#FFFF00',
            '#ADFF2F', '#7FFF00', '#32CD32', '#00FF00', '#00FA9A',
            '#00FFFF', '#87CEEB', '#4169E1', '#0000FF', '#8A2BE2'
        ],
        'default': [
            '#7B68EE', '#6495ED', '#4682B4', '#5F9EA0', '#7FFFD4',
            '#40E0D0', '#48D1CC', '#00CED1', '#5F9EA0', '#4682B4',
            '#6495ED', '#7B68EE', '#9370DB', '#8A2BE2', '#9932CC'
        ]
    };

    // Get the appropriate color palette
    const palette = colorPalettes[zoneType] || colorPalettes['default'];

    // Create a simple hash from the zone ID to get consistent colors
    let hash = 0;
    if (zoneId) {
        for (let i = 0; i < zoneId.toString().length; i++) {
            const char = zoneId.toString().charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
    }

    // Use absolute value and modulo to get a palette index
    const colorIndex = Math.abs(hash) % palette.length;
    return palette[colorIndex];
}

// Initialize the map
function initializeMap() {
    try {
        // Check if OpenLayers is loaded
        if (typeof ol === 'undefined') {
            console.error('OpenLayers (ol) is not loaded');
            return;
        }

        // Check if Proj4 is loaded
        if (typeof proj4 === 'undefined') {
            console.error('Proj4 is not loaded');
            return;
        }

        // Define Swiss coordinate system (EPSG:2056)
        proj4.defs('EPSG:2056', '+proj=somerc +lat_0=46.95240555555556 +lon_0=7.439583333333333 +k_0=1 +x_0=2600000 +y_0=1200000 +ellps=bessel +towgs84=674.374,15.056,405.346,0,0,0,0 +units=m +no_defs');
        ol.proj.proj4.register(proj4);

        // Create OSM layer with performance optimizations
        osmLayer = new ol.layer.Tile({
            source: new ol.source.OSM({
                attributions: '© OpenStreetMap contributors',
                // Use faster OSM servers
                url: 'https://{a-c}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                maxZoom: 19,
                // Performance optimizations
                cacheSize: 2048,  // Larger cache for background
                crossOrigin: 'anonymous'
            }),
            visible: true,
            zIndex: 0,  // Base layer
            // Background layer optimizations
            preload: 1,  // Preload one zoom level
            useInterimTilesOnError: false
        });

        // Create Swiss tile layer with performance optimizations
        swissLayer = new ol.layer.Tile({
            source: new ol.source.XYZ({
                url: 'https://wmts.geo.admin.ch/1.0.0/ch.swisstopo.pixelkarte-farbe/default/current/3857/{z}/{x}/{y}.jpeg',
                attributions: '© swisstopo',
                crossOrigin: 'anonymous',
                maxZoom: 18,
                cacheSize: 2048  // Larger cache for background
            }),
            visible: false,
            zIndex: 0,
            preload: 1,  // Preload one zoom level
            useInterimTilesOnError: false
        });

        // Create map view in web mercator (for compatibility, we'll transform data as needed)
        const center = ol.proj.fromLonLat([8.2275, 46.8182]); // Switzerland center
        const extent = ol.proj.transformExtent([5.96, 45.82, 10.49, 47.81], 'EPSG:4326', 'EPSG:3857');

        const view = new ol.View({
            center: center,
            zoom: 8,
            extent: extent
        });

        // Initialize map with basic controls
        map = new ol.Map({
            target: 'map',
            layers: [osmLayer, swissLayer],
            view: view
        });

        // Add scale line control manually
        map.addControl(new ol.control.ScaleLine());

        // Setup event handlers
        setupMapEventHandlers();

        // Make map globally available
        window.map = map;

    } catch (error) {
        console.error('Error initializing map:', error);
    }
}

// Setup map event handlers
function setupMapEventHandlers() {
    // Click handler for zone features and ÖREB lookup
    map.on('click', function(evt) {
        const pixel = map.getEventPixel(evt.originalEvent);
        const features = [];

        map.forEachFeatureAtPixel(pixel, function(feature, layer) {
            // Only consider zone layers
            if (layer && layer.get('name') && layer.get('name').startsWith('zones-')) {
                features.push(feature);
            }
        });

        if (features.length > 0) {
            const feature = features[0];
            const geometry = feature.getGeometry();
            const coordinate = evt.coordinate;

            // Highlight the clicked zone temporarily
            highlightClickedAreaTemporarily(geometry);
            showZonePopup(feature, coordinate);
        } else {
            // Clear highlight if clicking on empty area
            clearZoneHighlight();
        }

        // Always trigger ÖREB lookup on map click
        handleOEREBMapClick(evt);
    });
}

// Toggle between Swiss and OSM background layers
function toggleBackgroundLayer() {
    if (!map) return;

    if (currentBackgroundLayer === 'osm') {
        // Switch to Swiss layer
        osmLayer.setVisible(false);
        swissLayer.setVisible(true);
        currentBackgroundLayer = 'swiss';
    } else {
        // Switch to OSM layer
        swissLayer.setVisible(false);
        osmLayer.setVisible(true);
        currentBackgroundLayer = 'osm';
    }
}

// Update map with new zone data
function updateMapData(data) {
    if (!map || !data) return;

    // Clear any existing zone highlight when loading new zones
    clearZoneHighlight();

    // Update current municipality ID from data if available
    if (data.properties && data.properties.municipality) {
        currentMunicipalityId = data.properties.municipality;
    }

    const sourceId = `zones-${currentMunicipalityId || 'default'}`;

    // Remove ALL existing zone layers
    Object.keys(zoneLayers).forEach(layerId => {
        const layer = zoneLayers[layerId];
        if (layer) {
            map.removeLayer(layer);
        }
    });

    // Clear the layers object
    zoneLayers = {};

    // Create GeoJSON source
    // Note: Backend returns GeoJSON in WGS84 (EPSG:4326), transform to web mercator (EPSG:3857)
    const vectorSource = new ol.source.Vector({
        features: new ol.format.GeoJSON().readFeatures(data, {
            dataProjection: 'EPSG:4326',
            featureProjection: 'EPSG:3857'
        })
    });

    // Create style function for zones
    const styleFunction = function(feature) {
        const properties = feature.getProperties();
        const zoneColor = generateZoneColor(properties.id, properties.zone_type);

        // Convert hex color to rgba
        const hexToRgba = (hex, alpha) => {
            const r = parseInt(hex.slice(1, 3), 16);
            const g = parseInt(hex.slice(3, 5), 16);
            const b = parseInt(hex.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, ${alpha})`;
        };

        return [
            // Fill style
            new ol.style.Style({
                fill: new ol.style.Fill({
                    color: hexToRgba(zoneColor, 0.6)
                }),
                stroke: new ol.style.Stroke({
                    color: '#2c3e50',
                    width: 1
                })
            })
        ];
    };

    // Create vector layer with high z-index to appear above WMS layers
    const vectorLayer = new ol.layer.Vector({
        source: vectorSource,
        style: styleFunction,
        zIndex: 2000  // High z-index to ensure municipality zones appear on top
    });

    // Set layer name for identification
    vectorLayer.set('name', sourceId);

    // Add layer to map
    map.addLayer(vectorLayer);

    // Store layer reference
    zoneLayers[sourceId] = vectorLayer;

    // Fit map to data bounds
    if (data.features && data.features.length > 0) {
        try {
            const extent = vectorSource.getExtent();
            if (extent && !ol.extent.isEmpty(extent)) {
                map.getView().fit(extent, {
                    padding: [50, 50, 50, 50],
                    duration: 1000
                });
            }
        } catch (error) {
            console.error('Error fitting map bounds:', error);
        }
    }

    // Update current municipality
    if (data.metadata && data.metadata.municipality_id) {
        currentMunicipalityId = data.metadata.municipality_id;

        // Enable zoom to municipality button
        const zoomButton = document.getElementById('zoom-to-municipality');
        if (zoomButton) {
            zoomButton.disabled = false;
        }
    }
}

// Show popup for zone feature
function showZonePopup(feature, coordinate) {
    // Close existing popup
    if (currentPopup) {
        map.removeOverlay(currentPopup);
    }

    const properties = feature.getProperties();
    const popupHTML = createZonePopupHTML(properties);

    // Create popup element
    const popupElement = document.createElement('div');
    popupElement.className = 'ol-popup';
    popupElement.innerHTML = `
        <a href="#" class="ol-popup-closer"></a>
        <div class="ol-popup-content">${popupHTML}</div>
    `;

    // Create overlay
    currentPopup = new ol.Overlay({
        element: popupElement,
        positioning: 'bottom-center',
        stopEvent: true,
        offset: [0, -50]
    });

    // Add close functionality
    const closer = popupElement.querySelector('.ol-popup-closer');
    closer.onclick = function() {
        map.removeOverlay(currentPopup);
        currentPopup = null;
        return false;
    };

    // Add popup to map
    map.addOverlay(currentPopup);
    currentPopup.setPosition(coordinate);
}

// Create HTML for zone popup
function createZonePopupHTML(properties) {
    let html = '<div class="zone-popup">';

    html += `<h4>${properties.zone_name || 'Zone'}</h4>`;
    html += `<p><strong>Type:</strong> ${properties.zone_type || 'Unknown'}</p>`;

    if (properties.municipality_code) {
        html += `<p><strong>Municipality:</strong> ${properties.municipality_code}</p>`;
    }

    if (properties.area) {
        html += `<p><strong>Area:</strong> ${properties.area.toLocaleString('de-CH')} m²</p>`;
    }

    if (properties.id) {
        html += `<button onclick="showZoneDetails('${properties.id}')">View Details</button>`;
    }

    html += '</div>';
    return html;
}

// Show zone details
function showZoneDetails(zoneId) {
    const modal = document.getElementById('zone-details-modal');
    if (!modal) return;

    modal.classList.remove('hidden');

    const contentDiv = document.getElementById('zone-details-content');
    if (contentDiv) {
        contentDiv.innerHTML = `
            <div class="zone-details-loading">
                <div class="loading-spinner"></div>
                <span>Loading zone details...</span>
            </div>
        `;

        fetch(`/api/zones/${zoneId}`, {
            headers: { 'HX-Request': 'true' }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.text();
        })
        .then(html => {
            contentDiv.innerHTML = html;
        })
        .catch(error => {
            contentDiv.innerHTML = `<div class="zone-details-error"><p>Error loading zone details: ${error.message}</p></div>`;
        });
    }

    // Close any open popups
    if (currentPopup) {
        map.removeOverlay(currentPopup);
        currentPopup = null;
    }
}

// Zoom to a specific zone
function zoomToZone(zoneId) {
    const modal = document.getElementById('zone-details-modal');

    if (modal && !modal.classList.contains('hidden')) {
        fetch(`/api/zones/${zoneId}`)
            .then(response => response.json())
            .then(zone => {
                if (zone && zone.properties && zone.properties.geometry) {
                    try {
                        const geometry = typeof zone.properties.geometry === 'string'
                            ? JSON.parse(zone.properties.geometry)
                            : zone.properties.geometry;
                        zoomToGeometry(geometry);
                        return;
                    } catch (error) {
                        // Fallback to searching map data
                    }
                }
                searchMapDataForZone(zoneId);
            })
            .catch(() => {
                searchMapDataForZone(zoneId);
            });
    } else {
        searchMapDataForZone(zoneId);
    }
}

// Helper function to search for zone in map data sources
function searchMapDataForZone(zoneId) {
    // Search through zone layers
    Object.keys(zoneLayers).forEach(layerId => {
        const layer = zoneLayers[layerId];
        if (layer && layer.getSource) {
            const source = layer.getSource();
            if (source && source.getFeatures) {
                const features = source.getFeatures();
                const feature = features.find(f => f.get('id') === zoneId);
                if (feature) {
                    const geometry = feature.getGeometry();
                    if (geometry) {
                        zoomToOLGeometry(geometry);
                        return;
                    }
                }
            }
        }
    });
}

// Helper function to zoom to an OpenLayers geometry
function zoomToOLGeometry(geometry) {
    try {
        const extent = geometry.getExtent();
        if (extent && !ol.extent.isEmpty(extent)) {
            map.getView().fit(extent, {
                padding: [100, 100, 100, 100],
                duration: 1200,
                maxZoom: 18
            });
        }
    } catch (error) {
        console.warn('Error fitting map bounds:', error);
    }
}

// Helper function to zoom to a GeoJSON geometry (for API responses)
function zoomToGeometry(geometry) {
    try {
        // Convert GeoJSON geometry to OpenLayers geometry
        // Backend returns geometry in WGS84, need to transform to web mercator
        const olGeometry = new ol.format.GeoJSON().readGeometry(geometry, {
            dataProjection: 'EPSG:4326',
            featureProjection: 'EPSG:3857'
        });
        zoomToOLGeometry(olGeometry);
    } catch (error) {
        console.warn('Error fitting map bounds:', error);
    }
}

// Highlight a clicked zone temporarily
function highlightClickedAreaTemporarily(geometry) {
    // Remove existing highlight if any
    clearZoneHighlight();

    // Create highlight style
    const highlightStyle = new ol.style.Style({
        stroke: new ol.style.Stroke({
            color: '#ff6b35',
            width: 3
        }),
        fill: new ol.style.Fill({
            color: 'rgba(255, 107, 53, 0.1)'
        })
    });

    // Create feature from geometry
    const feature = new ol.Feature({
        geometry: geometry
    });

    // Create vector source and layer for highlight
    const highlightSource = new ol.source.Vector({
        features: [feature]
    });

    highlightLayer = new ol.layer.Vector({
        source: highlightSource,
        style: highlightStyle,
        zIndex: 2100  // Even higher z-index for highlights
    });

    // Add highlight layer to map
    map.addLayer(highlightLayer);
}

// Clear zone highlight
function clearZoneHighlight() {
    if (highlightLayer) {
        map.removeLayer(highlightLayer);
        highlightLayer = null;
    }
}

// Clear all zone layers from the map
function clearZones() {
    hideMapLoading();
    clearZoneHighlight();

    // Remove all zone layers
    Object.keys(zoneLayers).forEach(layerId => {
        const layer = zoneLayers[layerId];
        if (layer) {
            map.removeLayer(layer);
        }
    });

    zoneLayers = {};
    currentMunicipalityId = null;
}



// Handle data source selection changes
function handleDataSourceChange(dataSourceId, isSelected) {
    if (isSelected) {
        selectedDataSource = dataSourceId;

        // Check if this is a Nutzungsplanung data source and manage WMS layers
        if (isNutzungsplanungDataSource(dataSourceId)) {
            showNutzungsplanungWMSLayers();
            showWMSOpacityControl();
        } else {
            // Clean up WMS layers when switching to non-Nutzungsplanung sources
            cleanupWMSLayers();
            hideWMSOpacityControl();
        }
    } else {
        selectedDataSource = null;
        // Clean up WMS layers when deselecting Nutzungsplanung
        cleanupWMSLayers();
        hideWMSOpacityControl();
    }
}

// Show WMS opacity control
function showWMSOpacityControl() {
    const opacityControl = document.querySelector('.wms-opacity-control');
    if (opacityControl) {
        opacityControl.style.display = 'flex';
    }
}

// Hide WMS opacity control
function hideWMSOpacityControl() {
    const opacityControl = document.querySelector('.wms-opacity-control');
    if (opacityControl) {
        opacityControl.style.display = 'none';
    }
}

// Check if a data source is a Nutzungsplanung source
function isNutzungsplanungDataSource(dataSourceId) {
    // Data source 820737 is the main Geodienste Nutzungsplanung source
    // We can extend this list as needed
    const nutzungsplanungSources = ['820737'];
    return nutzungsplanungSources.includes(dataSourceId);
}

// WMS Layer Configuration - Include ALL available layers with text/labels
const WMS_ENDPOINTS = [
    // Geodienste.ch layers
    {
        id: 'geodienste_ch_grundnutzung',
        name: 'Geodienste.ch Grundnutzung',
        url: 'https://geodienste.ch/db/npl_nutzungsplanung_v1_2_0/deu',
        layers: 'grundnutzung',
        version: '1.3.0',
        zIndex: 500,
        opacity: 0.6
    },
    {
        id: 'geodienste_ch_ueberlagernde_flaechen',
        name: 'Geodienste.ch Überlagernde (Flächen)',
        url: 'https://geodienste.ch/db/npl_nutzungsplanung_v1_2_0/deu',
        layers: 'ueberlagernde_nutzungsplaninhalte_flaechenbezogene_festlegungen',
        version: '1.3.0',
        zIndex: 510,
        opacity: 0.5
    },
    {
        id: 'geodienste_ch_ueberlagernde_linien',
        name: 'Geodienste.ch Überlagernde (Linien)',
        url: 'https://geodienste.ch/db/npl_nutzungsplanung_v1_2_0/deu',
        layers: 'ueberlagernde_nutzungsplaninhalte_linienbezogene_festlegungen',
        version: '1.3.0',
        zIndex: 520,
        opacity: 0.7
    },
    {
        id: 'geodienste_ch_ueberlagernde_punkte',
        name: 'Geodienste.ch Überlagernde (Punkte)',
        url: 'https://geodienste.ch/db/npl_nutzungsplanung_v1_2_0/deu',
        layers: 'ueberlagernde_nutzungsplaninhalte_punktbezogene_festlegungen',
        version: '1.3.0',
        zIndex: 530,
        opacity: 0.8
    },
    {
        id: 'geodienste_ch_verfuegbarkeit',
        name: 'Geodienste.ch Verfügbarkeit',
        url: 'https://geodienste.ch/db/npl_nutzungsplanung_v1_2_0/deu',
        layers: 'verfuegbarkeit_kantone,verfuegbarkeit_beschriftungen',
        version: '1.3.0',
        zIndex: 540,
        opacity: 0.7
    },

    // Zurich layers - Base layers
    {
        id: 'zh_seen',
        name: 'Zurich Seen',
        url: 'https://wms.zh.ch/OGDARPOerebZH',
        layers: 'seen',
        version: '1.3.0',
        zIndex: 600,
        opacity: 0.7
    },
    {
        id: 'zh_uebersichtskarte',
        name: 'Zurich Übersichtskarte',
        url: 'https://wms.zh.ch/OGDARPOerebZH',
        layers: 'uebersichtskarte',
        version: '1.3.0',
        zIndex: 610,
        opacity: 0.5
    },
    {
        id: 'zh_grenzen',
        name: 'Zurich Grenzen',
        url: 'https://wms.zh.ch/OGDARPOerebZH',
        layers: 'grenzen,gemeindegrenzen',
        version: '1.3.0',
        zIndex: 620,
        opacity: 0.7
    },

    // Zurich layers - Nutzungsplanung
    {
        id: 'zh_nutzungsplanung',
        name: 'Zurich Grundnutzung',
        url: 'https://wms.zh.ch/OGDARPOerebZH',
        layers: 'nutzungsplanung-gemeinden',
        version: '1.3.0',
        zIndex: 630,
        opacity: 0.6
    },
    {
        id: 'zh_nutzungsplanung_legende',
        name: 'Zurich Nutzungsplanung Legende',
        url: 'https://wms.zh.ch/OGDARPOerebZH',
        layers: 'nutzungsplanung-legende',
        version: '1.3.0',
        zIndex: 640,
        opacity: 0.8
    },
    {
        id: 'zh_sondernutzungszonen',
        name: 'Zurich Sondernutzungszonen',
        url: 'https://wms.zh.ch/OGDARPOerebZH',
        layers: 'sondernutzungszonen-f-gemeinden,sondernutzungszonen-l-gemeinden,sondernutzungszonen-p-gemeinden',
        version: '1.3.0',
        zIndex: 650,
        opacity: 0.6
    },
    {
        id: 'zh_planungszonen',
        name: 'Zurich Planungszonen',
        url: 'https://wms.zh.ch/OGDARPOerebZH',
        layers: 'planungszonen,quartierplan',
        version: '1.3.0',
        zIndex: 660,
        opacity: 0.6
    },
    {
        id: 'zh_laermempfindlichkeit',
        name: 'Zurich Lärmempfindlichkeitsstufen',
        url: 'https://wms.zh.ch/OGDARPOerebZH',
        layers: 'laermempfindlichkeitsstufen',
        version: '1.3.0',
        zIndex: 670,
        opacity: 0.5
    },

    // Zurich layers - Umwelt
    {
        id: 'zh_grundwasser',
        name: 'Zurich Grundwasserschutz',
        url: 'https://wms.zh.ch/OGDARPOerebZH',
        layers: 'grundwasserschutzzonen,grundwasserschutzareale',
        version: '1.3.0',
        zIndex: 680,
        opacity: 0.5
    },
    {
        id: 'zh_wald',
        name: 'Zurich Wald',
        url: 'https://wms.zh.ch/OGDARPOerebZH',
        layers: 'waldreservate,waldabstandslinien,waldgrenzen',
        version: '1.3.0',
        zIndex: 690,
        opacity: 0.6
    },
    {
        id: 'zh_gewaesser',
        name: 'Zurich Gewässer',
        url: 'https://wms.zh.ch/OGDARPOerebZH',
        layers: 'gewaesserabstandslinien,gewaesserraum,verzicht-gewaesserraum',
        version: '1.3.0',
        zIndex: 700,
        opacity: 0.6
    },
    {
        id: 'zh_baulinien',
        name: 'Zurich Baulinien',
        url: 'https://wms.zh.ch/OGDARPOerebZH',
        layers: 'baulinien',
        version: '1.3.0',
        zIndex: 710,
        opacity: 0.7
    },

    // Zurich layers - Text/Labels
    {
        id: 'zh_beschriftung',
        name: 'Zurich Beschriftung',
        url: 'https://wms.zh.ch/OGDARPOerebZH',
        layers: 'nutzungsplanung-beschriftung-gemeinden',
        version: '1.3.0',
        zIndex: 720,
        opacity: 0.8
    },

    // Graubünden layers
    {
        id: 'gr_bauzonen',
        name: 'Graubünden Bauzonen',
        url: 'https://wms.geo.gr.ch/bauzonen_graubuenden',
        layers: 'Bauzonen_Hauptnutzungen',
        version: '1.3.0',
        zIndex: 800,
        opacity: 0.6
    },
    {
        id: 'gr_text',
        name: 'Graubünden Text',
        url: 'https://wms.geo.gr.ch/bauzonen_graubuenden',
        layers: 'Text_Zoombereich_oben,Text_Zoombereich_unten',
        version: '1.3.0',
        zIndex: 810,
        opacity: 0.8
    }
];

// Create WMS layers for Nutzungsplanung
function createNutzungsplanungWMSLayers() {
    if (!map) return;

    WMS_ENDPOINTS.forEach(endpoint => {
        if (wmsLayers[endpoint.id]) {
            return; // Layer already exists
        }

        try {
            // Create WMS tile source
            const wmsSource = new ol.source.TileWMS({
                url: endpoint.url,
                params: {
                    'LAYERS': endpoint.layers,
                    'VERSION': endpoint.version,
                    'FORMAT': 'image/png',
                    'TRANSPARENT': true,
                    'STYLES': '',
                    'TILED': true
                },
                serverType: 'mapserver',
                crossOrigin: 'anonymous'
            });

            // Create WMS tile layer
            const wmsLayer = new ol.layer.Tile({
                source: wmsSource,
                visible: false,
                opacity: endpoint.opacity,
                zIndex: endpoint.zIndex
            });

            // Set layer properties for identification
            wmsLayer.set('name', `wms-${endpoint.id}`);
            wmsLayer.set('title', endpoint.name);

            // Store layer reference (don't add to map yet - controlled by opacity)
            wmsLayers[endpoint.id] = wmsLayer;

        } catch (error) {
            console.error(`Error creating WMS layer ${endpoint.name}:`, error);
        }
    });
}

// Show Nutzungsplanung WMS layers
function showNutzungsplanungWMSLayers() {
    // Check current opacity slider value
    const opacitySlider = document.getElementById('wms-opacity-slider');
    const currentOpacity = opacitySlider ? parseInt(opacitySlider.value) : 60;

    // If opacity is 0%, treat it like switching to a different data source - clean up everything
    if (currentOpacity == 0) {
        cleanupWMSLayers();
        return;
    }

    // Create layers if they don't exist yet (lazy loading)
    if (Object.keys(wmsLayers).length === 0) {
        createNutzungsplanungWMSLayers();
    }

    // Add layers to map and set opacity
    Object.values(wmsLayers).forEach(layer => {
        if (layer) {
            if (!map.getLayers().getArray().includes(layer)) {
                map.addLayer(layer);
            }
            layer.setVisible(true);
            layer.setOpacity(currentOpacity / 100);
        }
    });
}

// Clean up WMS layers to free memory (call when switching away from Nutzungsplanung)
function cleanupWMSLayers() {
    Object.keys(wmsLayers).forEach(layerId => {
        const layer = wmsLayers[layerId];
        if (layer) {
            // Clear the layer's tile cache
            const source = layer.getSource();
            if (source && source.clear) {
                source.clear();
            }

            // Remove layer from map
            map.removeLayer(layer);
        }
    });

    // Clear the layers object
    wmsLayers = {};
}

// Update WMS layer opacity
function updateWMSOpacity(value) {
    const opacity = value / 100; // Convert percentage to decimal

    if (value == 0) {
        // At 0% opacity, behave exactly like switching to a different data source
        cleanupWMSLayers();
    } else {
        // At any opacity > 0%, show layers with the specified opacity
        // Create layers if they don't exist yet (moving from 0% to >0%)
        if (Object.keys(wmsLayers).length === 0) {
            createNutzungsplanungWMSLayers();
        }

        // Add layers to map and set opacity
        Object.values(wmsLayers).forEach(layer => {
            if (layer) {
                // Add layer back to map if it's not already there
                if (!map.getLayers().getArray().includes(layer)) {
                    map.addLayer(layer);
                }
                layer.setVisible(true);
                layer.setOpacity(opacity);
            }
        });
    }

    // Update the displayed percentage value
    const opacityValueElement = document.querySelector('.opacity-value');
    if (opacityValueElement) {
        opacityValueElement.textContent = value + '%';
    }
}

// Show/hide map loading indicator
function showMapLoading() {
    const indicator = document.getElementById('map-loading-indicator');
    if (indicator) {
        indicator.classList.remove('hidden');
    }
}

function hideMapLoading() {
    const indicator = document.getElementById('map-loading-indicator');
    if (indicator) {
        indicator.classList.add('hidden');
    }
}

// Load zones for a municipality via HTMX event
function loadZonesForMunicipality(municipality, zoneType) {
    showMapLoading();

    let geoJsonUrl = `/api/zones/municipality/${encodeURIComponent(municipality)}/geojson`;
    if (zoneType && zoneType.trim()) {
        geoJsonUrl += `?zone_type=${encodeURIComponent(zoneType)}`;
    }

    fetch(geoJsonUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(geoJsonData => {
            if (typeof updateMapData === 'function') {
                updateMapData(geoJsonData);
            }
        })
        .catch(() => {})
        .finally(() => {
            hideMapLoading();
        });
}

// Setup button event handlers and initialize map
document.addEventListener('DOMContentLoaded', function() {
    initializeMap();
    setupButtonHandlers();
    setupHTMXEventHandlers();
});

function setupButtonHandlers() {
    const toggleButton = document.getElementById('toggle-layers');
    if (toggleButton && !toggleButton.hasAttribute('data-handler-attached')) {
        toggleButton.addEventListener('click', toggleBackgroundLayer);
        toggleButton.setAttribute('data-handler-attached', 'true');
    }
}

function setupHTMXEventHandlers() {
    // Handle GeoJSON data loading
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.responseURL.includes('/geojson')) {
            try {
                const data = JSON.parse(event.detail.xhr.responseText);
                if (window.updateMapData) {
                    window.updateMapData(data);
                }
            } catch (error) {
                console.error('Error parsing GeoJSON response:', error);
            }
        }

        // Handle ÖREB requests completion
        if (event.detail.xhr.responseURL.includes('/api/oereb/lookup')) {
            hideOEREBLoading();
        }
    });

    // Handle loading states
    document.body.addEventListener('htmx:beforeRequest', function(event) {
        const target = event.target;
        if (target) {
            target.classList.add('loading');
        }
    });

    document.body.addEventListener('htmx:afterRequest', function(event) {
        const target = event.target;
        if (target) {
            target.classList.remove('loading');
        }
    });

    // Handle errors
    document.body.addEventListener('htmx:responseError', function(event) {
        console.error('HTMX request failed:', event.detail);

        // Handle ÖREB request errors
        if (event.detail.xhr.responseURL.includes('/api/oereb/lookup')) {
            hideOEREBLoading();
        }
    });

    // Handle HTMX send errors (network issues, etc.)
    document.body.addEventListener('htmx:sendError', function(event) {
        console.error('HTMX send error:', event.detail);

        // Handle ÖREB request send errors
        if (event.detail.requestConfig && event.detail.requestConfig.path &&
            event.detail.requestConfig.path.includes('/api/oereb/lookup')) {
            hideOEREBLoading();
        }
    });

    // Setup HTMX event listener for zone loading
    const mapElement = document.getElementById('map');
    if (mapElement) {
        mapElement.addEventListener('load-zones', function(event) {
            const {municipality, zoneType} = event.detail;
            loadZonesForMunicipality(municipality, zoneType);
        });
    } else {
        console.error('Map element not found when setting up event listeners');
    }
}

// Zone item click handler
function handleZoneItemClick(zoneId) {
    htmx.ajax('GET', `/api/zones/${zoneId}`, {
        target: '#selected-zone-info'
    });
}

// ÖREB functionality
function handleOEREBMapClick(evt) {
    try {
        // Get click coordinates in Web Mercator (EPSG:3857)
        const coordinate = evt.coordinate;

        // Transform to Swiss LV95 (EPSG:2056) for ÖREB API
        const swissCoordinate = ol.proj.transform(coordinate, 'EPSG:3857', 'EPSG:2056');
        const x = swissCoordinate[0];
        const y = swissCoordinate[1];

        // Show ÖREB sidebar if hidden
        showOEREBSidebar();

        // Show loading indicator
        showOEREBLoading();

        // Make HTMX request to ÖREB API
        htmx.ajax('POST', `/api/oereb/lookup?x=${x}&y=${y}`, {
            target: '#oereb-sidebar-content',
            swap: 'innerHTML'
        });

    } catch (error) {
        console.error('Error in ÖREB map click handler:', error);
        hideOEREBLoading();
    }
}

function showOEREBSidebar() {
    const sidebar = document.getElementById('oereb-sidebar');
    if (sidebar) {
        sidebar.classList.remove('hidden');
    }
}

function closeOEREBSidebar() {
    const sidebar = document.getElementById('oereb-sidebar');
    if (sidebar) {
        sidebar.classList.add('hidden');
    }
}

function showOEREBLoading() {
    const indicator = document.getElementById('oereb-loading-indicator');
    if (indicator) {
        indicator.classList.remove('hidden');
    }
}

function hideOEREBLoading() {
    const indicator = document.getElementById('oereb-loading-indicator');
    if (indicator) {
        indicator.classList.add('hidden');
    }
}

// Make ÖREB functions globally available
window.closeOEREBSidebar = closeOEREBSidebar;
window.showOEREBSidebar = showOEREBSidebar;

// Make functions available globally
window.updateMapData = updateMapData;
window.showZoneDetails = showZoneDetails;
window.zoomToZone = zoomToZone;
window.toggleBackgroundLayer = toggleBackgroundLayer;
window.setupButtonHandlers = setupButtonHandlers;
window.clearZones = clearZones;
window.loadZonesForMunicipality = loadZonesForMunicipality;
window.showMapLoading = showMapLoading;
window.hideMapLoading = hideMapLoading;
window.handleZoneItemClick = handleZoneItemClick;
window.handleDataSourceChange = handleDataSourceChange;
window.updateWMSOpacity = updateWMSOpacity;

