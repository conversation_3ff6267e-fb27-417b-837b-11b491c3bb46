/* Municipality Zones Viewer Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.header {
    background: #2c3e50;
    color: white;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.header-toggle-button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.header-toggle-button:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Main Layout */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.app-layout {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
}

/* Main content area with sidebar */
.main-content-with-sidebar {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* ÖREB Sidebar */
.oereb-sidebar {
    width: 410px;
    background: white;
    border-right: 2px solid #ddd;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.oereb-sidebar-header {
    padding: 1rem;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.oereb-sidebar-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.oereb-close-button {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #666;
    padding: 0.25rem;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.oereb-close-button:hover {
    background: #e9ecef;
}

.oereb-sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

/* Main content area (map + controls) */
.main-content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Map Section (Top) */
.map-section {
    height: 40vh;
    position: relative;
    background: #e8f4f8;
    border-bottom: 2px solid #ddd;
}

#map {
    width: 100%;
    height: 100%;
    min-height: 300px;
    background: #e8f4f8;
    position: relative;
}



/* Map Loading Indicator */
.map-loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    color: #555;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.map-loading-indicator.hidden {
    display: none;
}



/* Query Section (Middle) */
.query-section {
    background: white;
    border-bottom: 2px solid #ddd;
    padding: 1rem;
    min-height: 200px;
}

.query-controls {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
}

.query-controls-row {
    display: flex;
    gap: 2rem;
    align-items: flex-start;
}

.municipality-selection,
.zone-filters {
    flex: 1;
}

.query-status {
    flex: 1;
    display: flex;
    align-items: center;
}

.query-controls h3 {
    margin-bottom: 0.5rem;
    color: #2c3e50;
    font-size: 1rem;
}

/* Form Controls */
.filter-group {
    margin-bottom: 1rem;
}

select, input[type="text"] {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

select:focus, input[type="text"]:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Button styles */
.btn-primary {
    background: #3498db;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: background-color 0.2s;
}

.btn-primary:hover:not(:disabled) {
    background: #2980b9;
}

.btn-primary:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
}

.municipality-zones-button {
    margin-top: 0.5rem;
}

.municipality-zones-button.hidden {
    display: none;
}

.btn-small {
    background: #95a5a6;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8rem;
}

.btn-small:hover {
    background: #7f8c8d;
}

.btn-secondary {
    background: #6c757d;
}

.btn-secondary:hover {
    background: #5a6268;
}

/* Municipality Selector */
.municipality-selector {
    margin-bottom: 1rem;
}

.municipality-count {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
}

.no-municipalities {
    color: #e74c3c;
    font-style: italic;
}

/* No Results Styling */
.no-results-container {
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 1rem 0;
}

.no-results {
    color: #e74c3c;
    font-weight: 500;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.no-results-help {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
    text-align: left;
    display: inline-block;
}

/* Query status */
#query-status-message {
    padding: 0.75rem;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-style: italic;
    color: #666;
}

/* Combined Middle and Bottom Section with Proper Scrollbars */
.middle-bottom-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    background: white;
    min-height: 0;
}

/* Query Section (Middle) */
.query-section {
    background: white;
    border-bottom: 2px solid #ddd;
    padding: 1rem;
    flex-shrink: 0;
}

/* Results Section (Bottom) */
.results-section {
    flex: 1;
    background: white;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;
}

.results-header {
    padding: 1rem;
    border-bottom: 1px solid #ddd;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.results-header h3 {
    margin: 0;
    color: #2c3e50;
}

.results-summary {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

.results-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Tabs */
.results-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
}

.tab-button {
    background: none;
    border: none;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.2s;
}

.tab-button:hover {
    background: #e9ecef;
}

.tab-button.active {
    background: white;
    border-bottom-color: #3498db;
    font-weight: 500;
}

.tab-content {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    display: none;
    min-height: 0;
}

.tab-content.active {
    display: flex;
    flex-direction: column;
}

/* Ensure table container scrolls properly */
#zones-table {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}



/* API Query Info */
.api-query-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 0.75rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.api-query-label {
    margin: 0 0 0.25rem 0;
    font-weight: 600;
    color: #495057;
}

.api-query-link {
    color: #007bff;
    text-decoration: none;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85rem;
    word-break: break-all;
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.api-query-link:hover {
    color: #0056b3;
    background: #e7f3ff;
    border-color: #007bff;
    text-decoration: underline;
}

/* Cantonal ÖREB Links */
.cantonal-oereb-links {
    background: #e8f4fd;
    border: 1px solid #b8daff;
    border-radius: 4px;
    padding: 0.75rem;
    margin-bottom: 1rem;
}

.cantonal-links-label {
    font-size: 0.8rem;
    font-weight: 600;
    color: #004085;
    margin: 0 0 0.5rem 0;
}

.cantonal-links-container {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.cantonal-links-inline {
    flex-wrap: nowrap;
    justify-content: space-between;
}

/* Legal Links Section */
.legal-links-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.legal-link-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 0.75rem;
}

.link-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.link-detail {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.link-detail .label {
    font-weight: 600;
    color: #495057;
    min-width: 120px;
}

.link-detail .value a {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
}

.link-detail .value a:hover {
    color: #0056b3;
    text-decoration: underline;
}

.link-description {
    margin-top: 0.25rem;
    padding-left: 120px;
}

.link-description small {
    color: #6c757d;
    font-style: italic;
    font-size: 0.8rem;
}

.link-url {
    margin-top: 0.25rem;
    padding-left: 120px;
}

.link-url small {
    color: #6c757d;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.75rem;
    word-break: break-all;
}

/* Compact Legal Links */
.legal-links-compact {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.legal-link-compact {
    display: inline-block;
    background: #e3f2fd;
    color: #1976d2;
    text-decoration: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
    border: 1px solid #bbdefb;
    transition: all 0.2s ease;
}

.legal-link-compact:hover {
    background: #bbdefb;
    color: #0d47a1;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.cantonal-link {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-decoration: none;
    border-radius: 3px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.cantonal-link-xml {
    background: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.cantonal-link-xml:hover {
    background: #c3e6cb;
    color: #0f4419;
}

.cantonal-link-pdf {
    background: #f8d7da;
    color: #721c24;
    border-color: #f1b0b7;
}

.cantonal-link-pdf:hover {
    background: #f1b0b7;
    color: #5a161c;
}

.cantonal-link-json {
    background: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

.cantonal-link-json:hover {
    background: #ffeaa7;
    color: #664d03;
}

.cantonal-link-url {
    background: #e2e3e5;
    color: #383d41;
    border-color: #d6d8db;
}

.cantonal-link-url:hover {
    background: #d6d8db;
    color: #2e3338;
}

/* Theme Lists */
.themes-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.theme-item {
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.theme-item.concerned {
    background: #d4edda;
    border-color: #c3e6cb;
}

.theme-item.not-concerned {
    background: #f8f9fa;
    border-color: #dee2e6;
}

.theme-item.no-data {
    background: #fff3cd;
    border-color: #ffeaa7;
}

.theme-code {
    font-family: 'Courier New', monospace;
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.theme-text {
    font-size: 0.8rem;
    font-weight: 500;
}

/* General Information */
.general-info-list, .base-data-list, .glossary-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.general-info-item, .base-data-item, .glossary-item {
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.info-title, .data-title, .glossary-title {
    font-weight: 600;
    font-size: 0.85rem;
    color: #495057;
    margin-bottom: 0.5rem;
}

.info-content, .glossary-content {
    font-size: 0.8rem;
    line-height: 1.4;
    color: #6c757d;
}

.data-provider {
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.data-link a {
    font-size: 0.75rem;
    color: #007bff;
    text-decoration: none;
}

.data-link a:hover {
    text-decoration: underline;
}

/* Municipality and Office Info */
.municipality-info, .office-info, .certification-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.municipality-item, .office-item, .certification-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0.375rem 0;
    border-bottom: 1px solid #e9ecef;
}

.municipality-item:last-child, .office-item:last-child, .certification-item:last-child {
    border-bottom: none;
}

.municipality-item .label, .office-item .label, .certification-item .label {
    font-weight: 600;
    font-size: 0.75rem;
    color: #495057;
    min-width: 80px;
    flex-shrink: 0;
}

.municipality-item .value, .office-item .value, .certification-item .value {
    font-size: 0.75rem;
    color: #6c757d;
    text-align: right;
    flex-grow: 1;
}

.municipality-item .value a, .office-item .value a, .certification-item .value a {
    color: #007bff;
    text-decoration: none;
}

.municipality-item .value a:hover, .office-item .value a:hover, .certification-item .value a:hover {
    text-decoration: underline;
}

/* Zone Details Modal */
.modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.5) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 2000 !important;
}

.modal.hidden {
    display: none !important;
}

.modal-content {
    background: white !important;
    border-radius: 8px !important;
    max-width: 600px !important;
    width: 90% !important;
    max-height: 80vh !important;
    overflow-y: auto !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
    position: relative !important;
    margin: auto !important;
    flex-shrink: 0 !important;
}

.modal-header {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.close-button:hover {
    background: #e9ecef;
}

.modal-body {
    padding: 1rem;
}

/* Zone Details Content */
.zone-details-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.zone-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.zone-header h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
}

.zone-type-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    color: white;
}

.zone-type-grundnutzung {
    background: #3498db;
}

.property-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.property {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.property label {
    font-weight: 600;
    color: #555;
    font-size: 0.9rem;
}

.property span {
    color: #333;
    font-size: 0.95rem;
}

.additional-properties {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.additional-properties h5 {
    margin: 0 0 0.75rem 0;
    color: #2c3e50;
    font-size: 1rem;
}

.zone-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.zone-details-error {
    text-align: center;
    padding: 2rem;
    color: #e74c3c;
}

.zone-details-error p {
    margin-bottom: 1rem;
    font-style: italic;
}

/* Zone Details Loading Indicator */
.zone-details-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #666;
    font-style: italic;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #95a5a6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 0.75rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.zone-details-loading span {
    font-size: 0.9rem;
    color: #666;
}

/* Table styles */
.zones-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.zones-table th,
.zones-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.zones-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.zones-table tr:hover {
    background: #f8f9fa;
}

/* Right-align area column in zones table */
.zones-table th:nth-child(4),
.zones-table td:nth-child(4) {
    text-align: right;
    font-variant-numeric: tabular-nums;
}

.zones-summary {
    margin-top: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 4px;
}



/* Placeholder styles */
.placeholder {
    color: #666;
    font-style: italic;
    text-align: center;
    padding: 2rem;
}



/* Code formatting */
pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 1rem;
    overflow-x: auto;
    font-size: 0.8rem;
}

code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Zone popup styles */
.zone-popup {
    min-width: 200px;
}

.zone-popup h4 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
    font-size: 1rem;
}

.zone-popup p {
    margin: 0.25rem 0;
    font-size: 0.9rem;
}

.zone-popup .btn-small {
    margin-top: 0.5rem;
}

/* OpenLayers popup styles */
.ol-popup {
    position: absolute;
    background-color: white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #ccc;
    bottom: 12px;
    left: -50px;
    min-width: 200px;
}

.ol-popup:after, .ol-popup:before {
    top: 100%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
}

.ol-popup:after {
    border-color: rgba(255, 255, 255, 0);
    border-top-color: white;
    border-width: 10px;
    left: 48px;
    margin-left: -10px;
}

.ol-popup:before {
    border-color: rgba(204, 204, 204, 0);
    border-top-color: #ccc;
    border-width: 11px;
    left: 48px;
    margin-left: -11px;
}

.ol-popup-closer {
    text-decoration: none;
    position: absolute;
    top: 2px;
    right: 8px;
    font-size: 18px;
    color: #999;
}

.ol-popup-closer:after {
    content: "✖";
}

.ol-popup-content {
    margin: 0;
}

/* HTMX loading indicator */
.htmx-indicator {
    display: none;
    color: #666;
    font-style: italic;
    margin-left: 10px;
}

/* Responsive design */
@media (max-width: 768px) {
    .query-controls {
        flex-direction: column;
        gap: 1rem;
    }

    .map-section {
        height: 30vh;
    }

    .zones-table {
        font-size: 0.8rem;
    }

    .zones-table th,
    .zones-table td {
        padding: 0.5rem;
    }

    .zone-popup {
        min-width: 150px;
    }

    .zone-popup h4 {
        font-size: 0.9rem;
    }

    .zone-popup p {
        font-size: 0.8rem;
    }

    /* ÖREB Sidebar responsive */
    .main-content-with-sidebar {
        flex-direction: column;
    }

    .oereb-sidebar {
        width: 100%;
        height: 40vh;
        border-right: none;
        border-bottom: 2px solid #ddd;
        order: 2;
    }

    .main-content-area {
        order: 1;
    }

    .oereb-welcome {
        padding: 1rem;
    }

    .oereb-data-container {
        font-size: 0.85rem;
    }

    .property-item,
    .restriction-detail,
    .provision-detail,
    .document-detail {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .label {
        min-width: auto;
    }

    .value {
        text-align: left;
    }
}



/* Number formatting utilities */
.number {
    text-align: right;
    font-variant-numeric: tabular-nums;
}

.area-value {
    text-align: right;
    font-variant-numeric: tabular-nums;
    display: inline-block;
    min-width: 80px;
}

.count-value {
    text-align: right;
    font-variant-numeric: tabular-nums;
    display: inline-block;
    min-width: 30px;
}

/* ÖREB Styles */
.oereb-data-container {
    font-size: 0.9rem;
    line-height: 1.4;
}

.oereb-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.oereb-header h3 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.property-summary {
    font-size: 0.85rem;
    color: #666;
    line-height: 1.3;
}

.oereb-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f0f0f0;
}

.oereb-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.oereb-section h4 {
    margin: 0 0 0.75rem 0;
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 600;
}

.property-details,
.restrictions-list,
.legal-provisions-list,
.documents-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.property-item,
.restriction-detail,
.provision-detail,
.document-detail {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 0.25rem 0;
}

.restriction-item,
.legal-provision-item,
.document-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 0.75rem;
}

.restriction-topic,
.provision-title,
.document-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.restriction-details,
.provision-details,
.document-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.label {
    font-weight: 500;
    color: #555;
    min-width: 80px;
    flex-shrink: 0;
}

.value {
    color: #333;
    text-align: right;
    word-break: break-word;
}

.value a {
    color: #007bff;
    text-decoration: none;
    font-size: 0.85rem;
}

.value a:hover {
    text-decoration: underline;
}

.no-data-message {
    text-align: center;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 4px;
    color: #666;
}

.no-data-message .note {
    font-size: 0.8rem;
    font-style: italic;
    margin-top: 0.5rem;
}

.oereb-footer {
    margin-top: 1rem;
    padding-top: 0.75rem;
    border-top: 1px solid #eee;
    text-align: center;
}

.coordinates-info {
    color: #999;
    font-size: 0.75rem;
}

/* ÖREB Error Styles */
.oereb-error-container {
    font-size: 0.9rem;
}

.oereb-error {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.error-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.error-message h4 {
    margin: 0 0 0.5rem 0;
    color: #856404;
    font-size: 1rem;
}

.error-message p {
    margin: 0.25rem 0;
    color: #856404;
}

.error-help {
    font-size: 0.85rem;
    color: #6c5700;
    font-style: italic;
    margin-top: 0.5rem;
}

.property-info {
    background: rgba(255, 255, 255, 0.5);
    padding: 0.5rem;
    border-radius: 3px;
    margin: 0.5rem 0;
    font-size: 0.85rem;
    text-align: left;
}

.error-actions {
    margin-top: 1rem;
}

.retry-button {
    background: #856404;
    color: white;
}

.retry-button:hover {
    background: #6c5700;
}

.error-footer {
    margin-top: 0.75rem;
    padding-top: 0.5rem;
    border-top: 1px solid rgba(133, 100, 4, 0.2);
}

.alternative-sources {
    font-size: 0.85rem;
    line-height: 1.4;
}

.alternative-sources ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.alternative-sources li {
    margin-bottom: 0.25rem;
}

.alternative-sources a {
    color: #007bff;
    text-decoration: none;
}

.alternative-sources a:hover {
    text-decoration: underline;
}



/* ÖREB Loading Indicator */
.oereb-loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #666;
    font-style: italic;
}

.oereb-loading-indicator .loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 0.75rem;
}

/* ÖREB Welcome Message */
.oereb-welcome {
    text-align: center;
    padding: 2rem 1rem;
    color: #666;
    font-style: italic;
}

.oereb-welcome p {
    margin: 0.5rem 0;
    line-height: 1.4;
}

.oereb-welcome .note {
    font-size: 0.8rem;
    color: #999;
    margin-top: 1rem;
}

/* Utilities */
.hidden {
    display: none !important;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Data Source Selection */
.data-source-selection {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.data-source-selector {
    margin-top: 1rem;
}

.selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.selector-header h4 {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
}

.selector-summary {
    font-size: 0.9rem;
    color: #666;
}

.data-source-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.available-sources-section {
    margin-bottom: 1.5rem;
}

.planned-sources-section {
    margin-bottom: 1rem;
}

.planned-sources-section h5 {
    margin: 0 0 0.75rem 0;
    color: #666;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toggle-planned {
    cursor: pointer;
    color: #007bff;
    font-size: 0.9rem;
    font-weight: normal;
    user-select: none;
}

.toggle-planned:hover {
    color: #0056b3;
    text-decoration: underline;
}

.planned-sources {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 0.5rem;
    background: #fafafa;
}

.planned-sources .data-source-item {
    margin-bottom: 0.5rem;
    padding: 0.75rem;
}

.planned-sources .data-source-item:last-child {
    margin-bottom: 0;
}

.available-sources-section h5,
.planned-sources-section h5 {
    margin: 0 0 0.75rem 0;
    color: #333;
    font-size: 1rem;
    font-weight: 600;
}

.data-source-item {
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 1rem;
    transition: all 0.2s ease;
}

.data-source-item.available {
    border-color: #28a745;
    background: #f8fff9;
}

.data-source-item.unavailable {
    border-color: #ccc;
    background: #f8f9fa;
    opacity: 0.7;
}

.data-source-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.source-main {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.source-main input[type="radio"] {
    margin: 0;
    transform: scale(1.1);
}

/* Radio button selection styling */
.source-main input[type="radio"]:checked + .source-label {
    font-weight: 600;
    color: #2c3e50;
}

.source-main input[type="radio"]:checked + .source-label .source-name {
    color: #3498db;
}

.source-label {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-weight: 500;
}

.source-label.disabled {
    cursor: not-allowed;
    color: #999;
}

.source-name {
    flex: 1;
}

/* WMS Opacity Control */
.wms-opacity-control {
    display: none; /* Initially hidden, shown when WMS layers are active */
    align-items: center;
    gap: 0.5rem;
    margin-right: 0.5rem;
}

.opacity-label {
    font-size: 0.8rem;
    color: #666;
    font-weight: 500;
    margin: 0;
}

.opacity-slider {
    width: 80px;
    height: 4px;
    background: #ddd;
    border-radius: 2px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.opacity-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: #3498db;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.opacity-slider::-webkit-slider-thumb:hover {
    background: #2980b9;
}

.opacity-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: #3498db;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    transition: background-color 0.2s ease;
}

.opacity-slider::-moz-range-thumb:hover {
    background: #2980b9;
}

.opacity-value {
    font-size: 0.8rem;
    color: #666;
    font-weight: 500;
    min-width: 35px;
    text-align: right;
}

.info-button {
    background: #007bff;
    color: white;
    border: none;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;
}

.info-button:hover {
    background: #0056b3;
}

.source-description {
    margin-top: 0.5rem;
    padding-left: 2rem;
    font-size: 0.9rem;
    color: #666;
    font-style: italic;
}

/* Status badges */
.status-badge {
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    white-space: nowrap;
}

.status-badge.available {
    background: #d4edda;
    color: #155724;
}

.status-badge.unavailable {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.canton-issues {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    font-size: 0.75rem;
    max-width: 400px;
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.3;
}




