"""
Zone data models.
"""

from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from enum import Enum

class ZoneType(str, Enum):
    """Zone type enumeration."""
    GRUNDNUTZUNG = "grundnutzung"
    UEBERLAGERNDE_FLAECHEN = "ueberlagernde_flaechen"
    UEBERLAGERNDE_LINIEN = "ueberlagernde_linien"
    UEBERLAGERNDE_PUNKTE = "ueberlagernde_punkte"
    ZH_ZONENFLAECHE = "zh_zonenflaeche"
    ZH_UEBERLAGERNDE = "zh_ueberlagernde"

class Zone(BaseModel):
    """Zone data model."""
    id: str
    zone_type: ZoneType
    municipality_id: Optional[str] = None
    municipality_name: Optional[str] = None
    zone_code: Optional[str] = None
    zone_name: Optional[str] = None
    description: Optional[str] = None
    area: Optional[float] = None
    properties: Optional[Dict[str, Any]] = None
    
    class Config:
        from_attributes = True

class ZoneList(BaseModel):
    """List of zones."""
    zones: List[Zone]
    total: Optional[int] = None

class ZoneGeometry(BaseModel):
    """Zone with geometry for GeoJSON output."""
    id: str
    zone_type: ZoneType
    properties: Dict[str, Any]
    geometry: Dict[str, Any]  # GeoJSON geometry
