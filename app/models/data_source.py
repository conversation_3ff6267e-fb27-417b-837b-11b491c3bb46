"""
Data source models for Swiss geodata management.
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from enum import Enum


class DataSourceStatus(str, Enum):
    """Status of a data source."""
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    PROCESSING = "processing"
    ERROR = "error"


class GeoCategory(BaseModel):
    """Geographic category information."""
    id: int
    code: str
    name: str
    nameDe: Optional[str] = None
    nameFr: Optional[str] = None
    nameIt: Optional[str] = None
    nameRm: Optional[str] = None


class TechnicalEntry(BaseModel):
    """Technical entry within a legislative entry."""
    id: int
    label: str
    labelNumber: int
    description: str
    descriptionDe: Optional[str] = None
    descriptionFr: Optional[str] = None
    descriptionIt: Optional[str] = None
    dataModelUrl: Optional[str] = None
    modelDocumentationUrl: Optional[str] = None
    metadataUrls: List[Dict[str, Any]] = []
    geoCategory: Optional[GeoCategory] = None
    startDate: Optional[str] = None
    endDate: Optional[str] = None
    status: Optional[str] = None
    isHistorised: bool = False
    hideLabel: bool = False
    # Additional API fields
    dataOwner: Optional[str] = None
    dataProvider: Optional[str] = None
    contactPerson: Optional[str] = None
    contactEmail: Optional[str] = None
    contactPhone: Optional[str] = None
    updateFrequency: Optional[str] = None
    spatialExtent: Optional[str] = None
    temporalExtent: Optional[str] = None
    lineage: Optional[str] = None
    resolution: Optional[str] = None
    accuracy: Optional[str] = None


class LegislativeEntry(BaseModel):
    """Legislative entry from Swiss geodata API."""
    id: int
    labelNumber: int
    identifier: str
    title: str
    titleDe: Optional[str] = None
    titleFr: Optional[str] = None
    titleIt: Optional[str] = None
    technicalEntries: List[TechnicalEntry] = []
    downloadService: bool = False
    access: str = "public"
    oereb: bool = False


class LocalDataSource(BaseModel):
    """Local data source configuration."""
    id: str
    name: str
    description: str
    status: DataSourceStatus
    file_paths: List[str] = []
    legislative_entry_id: Optional[int] = None
    technical_entry_id: Optional[int] = None
    last_updated: Optional[str] = None
    metadata: Dict[str, Any] = {}


class CantonAvailability(BaseModel):
    """Canton availability information for a data source."""
    canton_abbreviation: str
    canton_name: str
    municipalities_with_data: int
    total_municipalities: int
    coverage_percentage: float


class DataSourceCantonInfo(BaseModel):
    """Canton coverage information for a data source."""
    total_cantons: int
    cantons_with_50_percent_plus: int
    cantons_with_data: List[CantonAvailability]
    cantons_without_data: List[CantonAvailability]

    @property
    def show_available_cantons(self) -> bool:
        """Whether to show available cantons (≥50% have data) or missing cantons (<50% have data)."""
        return self.cantons_with_50_percent_plus >= (self.total_cantons * 0.5)


class DataSourceInfo(BaseModel):
    """Combined information about a data source."""
    local: Optional[LocalDataSource] = None
    legislative: Optional[LegislativeEntry] = None
    canton_info: Optional[DataSourceCantonInfo] = None
    canton_summary: Optional[str] = None  # Compact summary for main list display

    @property
    def is_available(self) -> bool:
        """Check if data source is locally available."""
        return self.local is not None and self.local.status == DataSourceStatus.AVAILABLE

    @property
    def display_name(self) -> str:
        """Get display name for the data source."""
        if self.legislative:
            # Format: "labelNumber.technicalEntry.labelNumber Title"
            if self.legislative.technicalEntries:
                tech_entry = self.legislative.technicalEntries[0]
                return f"{self.legislative.labelNumber}.{tech_entry.labelNumber} {self.legislative.title}"
            return f"{self.legislative.labelNumber} {self.legislative.title}"
        elif self.local:
            return self.local.name
        return "Unknown Data Source"

    @property
    def description(self) -> str:
        """Get description for the data source."""
        if self.legislative and self.legislative.technicalEntries:
            return self.legislative.technicalEntries[0].description
        elif self.local:
            return self.local.description
        return ""


class DataSourceList(BaseModel):
    """List of data sources."""
    sources: List[DataSourceInfo]
    total: int
    available_count: int
    unavailable_count: int
