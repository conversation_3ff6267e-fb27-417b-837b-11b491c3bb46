"""
Municipality data models.
"""

from pydantic import BaseModel
from typing import List, Optional

class Municipality(BaseModel):
    """Municipality data model."""
    id: str
    name: str
    canton: str
    bfs_code: Optional[str] = None
    historical_code: Optional[str] = None
    has_data: bool = True

    class Config:
        from_attributes = True

class MunicipalityBounds(BaseModel):
    """Geographic bounds for a municipality."""
    min_x: float
    min_y: float
    max_x: float
    max_y: float

class MunicipalityList(BaseModel):
    """List of municipalities."""
    municipalities: List[Municipality]
    total: Optional[int] = None
