"""
FastAPI application entry point for Municipality Zones (Nutzungsplan) viewer.
"""

from fastapi import FastAPI, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates

from app.api import municipalities, zones, maps, data_sources, oereb

app = FastAPI(
    title="Municipality Zones Viewer",
    description="Swiss Municipality Zones (Nutzungsplan) visualization with spatial queries",
    version="1.0.0"
)

# Mount static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Templates
templates = Jinja2Templates(directory="app/templates")

# Include API routers
app.include_router(data_sources.router, prefix="/api/data-sources", tags=["data-sources"])
app.include_router(municipalities.router, prefix="/api/municipalities", tags=["municipalities"])
app.include_router(zones.router, prefix="/api/zones", tags=["zones"])
app.include_router(maps.router, prefix="/api/maps", tags=["maps"])
app.include_router(oereb.router, prefix="/api/oereb", tags=["oereb"])

@app.get("/")
async def root(request: Request):
    """Root endpoint - serves the main application page."""
    return templates.TemplateResponse("index.html", {"request": request})

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
