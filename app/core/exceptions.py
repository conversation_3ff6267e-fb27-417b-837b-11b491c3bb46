"""
Custom application exceptions.
"""

class MunicipalityZonesException(Exception):
    """Base exception for the application."""
    pass

class MunicipalityNotFoundError(MunicipalityZonesException):
    """Raised when a municipality is not found."""
    pass

class ZoneNotFoundError(MunicipalityZonesException):
    """Raised when a zone is not found."""
    pass

class SpatialQueryError(MunicipalityZonesException):
    """Raised when a spatial query fails."""
    pass

class DataNotAvailableError(MunicipalityZonesException):
    """Raised when required data is not available."""
    pass
