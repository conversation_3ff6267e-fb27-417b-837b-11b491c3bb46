"""
Application configuration settings.
"""

import os
from pydantic_settings import BaseSettings
from typing import Dict, Any

class Settings(BaseSettings):
    """Application settings."""

    # Application
    app_name: str = "Municipality Zones Viewer"
    debug: bool = False

    # Database
    duckdb_path: str = ":memory:"  # In-memory by default
    data_directory: str = "data/processed"
    boundaries_directory: str = "data/boundaries"

    # DuckDB performance settings
    duckdb_config: Dict[str, Any] = {
        "memory_limit": "4GB",
        "max_memory": "8GB",
        "threads": 8,
        "enable_parallel_processing": True,
        "chunk_size": 10000,
        "cache_size": "1GB",
        "enable_statistics": True
    }

    # Data sources
    npl_data_path: str = "data/processed/npl_files"
    zh_data_path: str = "data/processed/zh_files"
    swissboundaries_path: str = "data/boundaries/swissboundaries.parquet"

    # Data file mappings - NPL data now uses GPKG directly
    gpkg_file_path: str = "raw_input_data/820737/npl_nutzungsplanung_lv95/geopackage/npl_nutzungsplanung_v1_2_2056.gpkg"

    # Layer mappings for GPKG file
    gpkg_layers: Dict[str, str] = {
        "npl_grundnutzung": "grundnutzung",
        "npl_ueberlagernde_flaechen": "ueberlagernde_nutzungsplaninhalte_flaechen",
        "npl_ueberlagernde_linien": "ueberlagernde_nutzungsplaninhalte_linien",
        "npl_ueberlagernde_punkte": "ueberlagernde_nutzungsplaninhalte_punkte"
    }

    # Legacy data files (non-NPL sources still use parquet)
    data_files: Dict[str, str] = {
        "zh_zonenflaeche": "data/processed/zh_files/NP_GN_Zonenflaeche.parquet",
        "zh_zonenflaeche_geometrie": "data/processed/zh_files/NP_GN_Zonenflaeche_Geometrie.parquet",
        "zh_ueberlagernde_flaeche": "data/processed/zh_files/NP_UL_Flaeche.parquet",
        "zh_ueberlagernde_flaeche_geometrie": "data/processed/zh_files/NP_UL_Flaeche_Geometrie.parquet",
        "swiss_boundaries": "data/boundaries/swissboundaries.parquet"
    }

    # Spatial reference systems
    srid_config: Dict[str, int] = {
        "swiss_lv95": 2056,
        "wgs84": 4326,
        "web_mercator": 3857
    }

    # View definitions for common queries
    view_definitions: Dict[str, str] = {
        "all_grundnutzung": """
            SELECT
                'npl' as source,
                typ_kommunal_bezeichnung as zone_name,
                typ_kommunal_code as municipality_code,
                geom,
                ST_Area(geom) as area
            FROM npl_grundnutzung
            WHERE geom IS NOT NULL
        """,
        "all_municipalities": """
            SELECT DISTINCT
                tlm_hoheitsgebiet_name as name,
                tlm_hoheitsgebiet_name as id,
                'CH' as canton,
                geometry,
                ST_Centroid(geometry) as centroid,
                ST_Area(geometry) as area
            FROM swiss_municipalities
            WHERE tlm_hoheitsgebiet_name IS NOT NULL
        """
    }

    # Map settings
    default_zoom: int = 8
    default_center_lat: float = 46.8182
    default_center_lng: float = 8.2275

    # API settings
    max_zones_per_request: int = 1000

    # Spatial filtering settings
    # Minimum percentage of zone area that must be within municipality to include it
    # 0.8 = 80% of zone must be within municipality boundary
    spatial_filter_threshold: float = 0.8

    class Config:
        env_file = ".env"
        case_sensitive = False

    def get_data_file_path(self, key: str) -> str:
        """Get full path to a data file by key."""
        return self.data_files.get(key, "")

    def file_exists(self, key: str) -> bool:
        """Check if a data file exists by key."""
        path = self.get_data_file_path(key)
        return os.path.exists(path) if path else False

# Global settings instance
settings = Settings()

def get_data_path(filename: str) -> str:
    """Get full path to a data file."""
    return os.path.join(settings.data_directory, filename)

def get_boundaries_path(filename: str) -> str:
    """Get full path to a boundaries file."""
    return os.path.join(settings.boundaries_directory, filename)
