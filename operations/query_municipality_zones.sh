#!/bin/bash

# Query Municipality Zones - Main Operational Script
# This script queries zoning information for any Swiss municipality
# Usage: ./query_municipality_zones.sh [municipality_name] [optional_canton]

set -e  # Exit on any error

# Get script directory and load configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
source "$PROJECT_ROOT/config/data_sources.conf"

# Helper functions
get_timeout_cmd() {
    if command -v timeout >/dev/null 2>&1; then
        echo "timeout ${SPATIAL_QUERY_TIMEOUT}s"
    else
        echo ""
    fi
}

get_duckdb_prefix() {
    echo "INSTALL spatial; LOAD spatial;"
}

# Function to execute spatial query for any municipality
execute_spatial_query() {
    local municipality_name="$1"
    local canton_filter="$2"

    echo "=== Spatial Query: $municipality_name ==="
    echo ""

    # Check if data is prepared
    if [ ! -f "$PROJECT_ROOT/data/processed/parquet/npl_files/grundnutzung.parquet" ]; then
        echo "Error: Prepared data not found!"
        echo "Please run the preparation script first:"
        echo "  ./raw_input_data/prepare_data.sh"
        echo ""
        exit 1
    fi

    # Build canton filter clause
    local canton_clause=""
    if [ -n "$canton_filter" ]; then
        canton_clause="AND zones.$NPL_CANTON_COLUMN = '$canton_filter'"
        echo "Canton filter: $canton_filter"
    else
        echo "No canton filter (searching all cantons)"
    fi

    echo "Executing spatial intersection query..."
    echo ""

    # Execute the spatial query
    $(get_timeout_cmd) duckdb -c "
    $(get_duckdb_prefix)

    -- First, verify the municipality exists
    SELECT
        'Municipality found: ' || $SB_MUNICIPALITY_NAME_COLUMN || ' (BFS: ' || $SB_BFS_NUMBER_COLUMN || ')' AS info
    FROM
        st_read('$PROJECT_ROOT/data/input/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg', layer='$SWISSBOUNDARIES_MUNICIPALITY_LAYER')
    WHERE
        $SB_MUNICIPALITY_NAME_COLUMN = '$municipality_name'
    LIMIT 1;

    -- Main spatial intersection query
    SELECT
        zones.$NPL_KOMMUNAL_ZONE_COLUMN AS zone_name,
        zones.$NPL_KANTONAL_ZONE_COLUMN AS kantonal_equivalent,
        COUNT(*) AS feature_count,
        zones.$NPL_CANTON_COLUMN AS canton
    FROM
        st_read('$PROJECT_ROOT/raw_input_data/820737/npl_nutzungsplanung_lv95/geopackage/npl_nutzungsplanung_v1_2_2056.gpkg', layer='grundnutzung') AS zones
    JOIN
        st_read('$PROJECT_ROOT/data/input/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg', layer='$SWISSBOUNDARIES_MUNICIPALITY_LAYER') AS municipalities
    ON
        ST_Intersects(zones.$NPL_GEOMETRY_COLUMN, municipalities.$SB_GEOMETRY_COLUMN)
    WHERE
        municipalities.$SB_MUNICIPALITY_NAME_COLUMN = '$municipality_name'
        $canton_clause
    GROUP BY
        zones.$NPL_KOMMUNAL_ZONE_COLUMN, zones.$NPL_KANTONAL_ZONE_COLUMN, zones.$NPL_CANTON_COLUMN
    ORDER BY
        feature_count DESC
    LIMIT $MAX_RESULTS;
    "
}

# Main execution
if [ $# -eq 0 ]; then
    echo "=== Municipality Zone Query Tool ==="
    echo ""
    echo "Usage: $0 [municipality_name] [optional_canton]"
    echo ""
    echo "Examples:"
    echo "  $0 Steinhausen"
    echo "  $0 Steinhausen ZG"
    echo "  $0 Zurich ZH"
    echo "  $0 Basel BS"
    echo ""
    echo "To discover available municipalities, run:"
    echo "  ./operations/discover_municipalities.sh"
    echo ""
    echo "To prepare data (if not done yet), run:"
    echo "  ./raw_input_data/prepare_data.sh"
    echo ""
    exit 1
fi

execute_spatial_query "$1" "$2"
