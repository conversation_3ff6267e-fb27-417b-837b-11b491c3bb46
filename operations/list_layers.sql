-- List Data Layers - Inspection Tool
-- This script helps inspect available data layers and their structure

INSTALL spatial;
LOAD spatial;

-- Show available parquet files
.shell echo "=== Available Parquet Files ==="
.shell find data/processed/parquet -name "*.parquet" -type f 2>/dev/null || echo "No parquet files found. Run preparation first."

.shell echo ""
.shell echo "=== NPL Grundnutzung Sample Data ==="

-- Show sample data from NPL grundnutzung if available
SELECT 
    kanton,
    typ_kommunal_bezeichnung,
    typ_kantonal_bezeichnung,
    COUNT(*) as feature_count
FROM 
    'data/processed/parquet/npl_files/grundnutzung.parquet'
GROUP BY 
    kanton, typ_kommunal_bezeichnung, typ_kantonal_bezeichnung
ORDER BY 
    kanton, feature_count DESC
LIMIT 20;

.shell echo ""
.shell echo "=== SwissBoundaries3D Layers ==="

-- List SwissBoundaries3D layers if available
.shell ogrinfo data/input/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg 2>/dev/null | grep "1:" | head -10 || echo "SwissBoundaries3D not found"

.shell echo ""
.shell echo "=== Sample Municipality Data ==="

-- Show sample municipalities
SELECT 
    name as municipality_name,
    bfs_nummer as bfs_number,
    kantonsnummer as canton_number
FROM 
    st_read('data/input/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg', layer='tlm_hoheitsgebiet')
ORDER BY 
    name
LIMIT 10;
