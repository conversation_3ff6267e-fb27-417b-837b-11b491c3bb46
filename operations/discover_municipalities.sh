#!/bin/bash

# Discover Available Municipalities
# This script helps discover municipalities using both BFS CSV data and SwissBoundaries3D spatial data

set -e  # Exit on any error

# Get script directory and load configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
source "$PROJECT_ROOT/config/data_sources.conf"

echo "=== Municipality Discovery Tool ==="
echo ""

# Check if BFS CSV data exists
BFS_CSV="$PROJECT_ROOT/data/input/swiss_municipalities_bfs_snapshot.csv"
SWISSBOUNDARIES_GPKG="$PROJECT_ROOT/data/input/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg"

if [ ! -f "$BFS_CSV" ]; then
    echo "Warning: BFS municipality CSV not found at $BFS_CSV"
    echo "Some features will be limited."
    echo ""
fi

if [ ! -f "$SWISSBOUNDARIES_GPKG" ]; then
    echo "Warning: SwissBoundaries3D data not found!"
    echo "Spatial features will be limited."
    echo "Please run: ./raw_input_data/prepare_data.sh"
    echo ""
fi

# Function to search municipalities using BFS CSV (fast)
search_municipalities_bfs() {
    local search_term="$1"
    local canton_filter="$2"

    if [ ! -f "$BFS_CSV" ]; then
        echo "BFS CSV data not available. Use 'spatial' search instead."
        return 1
    fi

    echo "Searching BFS registry for municipalities containing: '$search_term'"
    if [ -n "$canton_filter" ]; then
        echo "Canton filter: $canton_filter"
    fi
    echo ""

    # Use awk to search the CSV file
    awk -F',' -v search="$search_term" -v canton="$canton_filter" '
    BEGIN {
        OFS="\t"
        print "BFS Code", "Municipality Name", "Level", "Parent"
        print "--------", "----------------", "-----", "------"
    }
    NR > 1 && $3 == 3 {  # Level 3 = municipalities
        name = $7
        gsub(/"/, "", name)  # Remove quotes
        if (tolower(name) ~ tolower(search)) {
            if (canton == "" || tolower(name) ~ tolower(canton)) {
                printf "%s\t%s\t%s\t%s\n", $2, name, $5, $6
            }
        }
    }' "$BFS_CSV" | head -20
}

# Function to search municipalities using spatial data (comprehensive)
search_municipalities_spatial() {
    local search_term="$1"
    local canton_filter="$2"

    if [ ! -f "$SWISSBOUNDARIES_GPKG" ]; then
        echo "SwissBoundaries3D data not available."
        return 1
    fi

    local canton_clause=""
    if [ -n "$canton_filter" ]; then
        canton_clause="AND kantonsnummer = (SELECT kantonsnummer FROM st_read('$SWISSBOUNDARIES_GPKG', layer='$SWISSBOUNDARIES_CANTON_LAYER') WHERE kanton = '$canton_filter' LIMIT 1)"
    fi

    echo "Searching spatial data for municipalities containing: '$search_term'"
    if [ -n "$canton_filter" ]; then
        echo "Canton filter: $canton_filter"
    fi
    echo ""

    duckdb -c "
    INSTALL spatial;
    LOAD spatial;

    SELECT
        $SB_MUNICIPALITY_NAME_COLUMN AS municipality_name,
        $SB_BFS_NUMBER_COLUMN AS bfs_number,
        $SB_CANTON_NUMBER_COLUMN AS canton_number
    FROM
        st_read('$SWISSBOUNDARIES_GPKG', layer='$SWISSBOUNDARIES_MUNICIPALITY_LAYER')
    WHERE
        LOWER($SB_MUNICIPALITY_NAME_COLUMN) LIKE LOWER('%$search_term%')
        $canton_clause
    ORDER BY
        $SB_MUNICIPALITY_NAME_COLUMN
    LIMIT 50;
    "
}

# Function to verify municipality exists in both sources
verify_municipality() {
    local municipality_name="$1"

    echo "=== Municipality Verification: $municipality_name ==="
    echo ""

    # Check BFS CSV
    if [ -f "$BFS_CSV" ]; then
        echo "BFS Registry:"
        local bfs_result=$(awk -F',' -v name="$municipality_name" '
        NR > 1 && $3 == 3 {  # Level 3 = municipalities
            muni_name = $7
            gsub(/"/, "", muni_name)
            if (tolower(muni_name) == tolower(name)) {
                printf "  ✓ Found: %s (BFS: %s, Historical: %s)\n", muni_name, $2, $1
                found = 1
            }
        }
        END { if (!found) print "  ✗ Not found in BFS registry" }
        ' "$BFS_CSV")
        echo "$bfs_result"
    else
        echo "BFS Registry: Not available"
    fi

    echo ""

    # Check SwissBoundaries3D
    if [ -f "$SWISSBOUNDARIES_GPKG" ]; then
        echo "SwissBoundaries3D:"
        duckdb -c "
        INSTALL spatial;
        LOAD spatial;

        SELECT
            CASE
                WHEN COUNT(*) > 0 THEN '  ✓ Found: ' || $SB_MUNICIPALITY_NAME_COLUMN || ' (BFS: ' || $SB_BFS_NUMBER_COLUMN || ')'
                ELSE '  ✗ Not found in SwissBoundaries3D'
            END AS result
        FROM
            st_read('$SWISSBOUNDARIES_GPKG', layer='$SWISSBOUNDARIES_MUNICIPALITY_LAYER')
        WHERE
            LOWER($SB_MUNICIPALITY_NAME_COLUMN) = LOWER('$municipality_name')
        LIMIT 1;
        " 2>/dev/null || echo "  ✗ Error accessing SwissBoundaries3D"
    else
        echo "SwissBoundaries3D: Not available"
    fi

    echo ""
}

# Function to list municipalities by canton
list_by_canton() {
    local canton="$1"

    echo "Municipalities in canton $canton:"
    echo ""

    duckdb -c "
    INSTALL spatial;
    LOAD spatial;

    SELECT
        $SB_MUNICIPALITY_NAME_COLUMN AS municipality_name,
        $SB_BFS_NUMBER_COLUMN AS bfs_number
    FROM
        st_read('$PROJECT_ROOT/data/input/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg', layer='$SWISSBOUNDARIES_MUNICIPALITY_LAYER') AS municipalities
    JOIN
        st_read('$PROJECT_ROOT/data/input/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg', layer='$SWISSBOUNDARIES_CANTON_LAYER') AS cantons
    ON
        municipalities.$SB_CANTON_NUMBER_COLUMN = cantons.kantonsnummer
    WHERE
        cantons.kanton = '$canton'
    ORDER BY
        $SB_MUNICIPALITY_NAME_COLUMN;
    "
}

# Function to show statistics
show_statistics() {
    echo "=== Municipality Statistics ==="
    echo ""

    duckdb -c "
    INSTALL spatial;
    LOAD spatial;

    SELECT
        'Total municipalities: ' || COUNT(*) AS statistic
    FROM
        st_read('$PROJECT_ROOT/data/input/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg', layer='$SWISSBOUNDARIES_MUNICIPALITY_LAYER');

    SELECT
        'Total cantons: ' || COUNT(*) AS statistic
    FROM
        st_read('$PROJECT_ROOT/data/input/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg', layer='$SWISSBOUNDARIES_CANTON_LAYER');
    "
}

# Main execution logic
case "${1:-help}" in
    "help"|"")
        echo "Usage: $0 [command] [parameters]"
        echo ""
        echo "Commands:"
        echo "  search [term]           Search municipalities by name"
        echo "  canton [canton_code]    List all municipalities in a canton"
        echo "  stats                   Show statistics"
        echo "  all                     List all municipalities (first 100)"
        echo ""
        echo "Examples:"
        echo "  $0 search Stein         # Find municipalities containing 'Stein'"
        echo "  $0 canton ZG            # List all municipalities in Zug canton"
        echo "  $0 stats                # Show total counts"
        echo ""
        ;;
    "search")
        if [ -z "$2" ]; then
            echo "Please provide a search term."
            echo "Example: $0 search Stein"
            exit 1
        fi
        echo "Searching for municipalities containing: '$2'"
        echo ""
        search_municipalities "$2" "$3"
        ;;
    "canton")
        if [ -z "$2" ]; then
            echo "Please provide a canton code (e.g., ZG, ZH, BE)."
            echo "Example: $0 canton ZG"
            exit 1
        fi
        list_by_canton "$2"
        ;;
    "stats")
        show_statistics
        ;;
    "all")
        echo "All municipalities (first 100):"
        echo ""
        search_municipalities "" ""
        ;;
    *)
        echo "Unknown command: $1"
        echo "Run '$0 help' for usage information."
        exit 1
        ;;
esac
