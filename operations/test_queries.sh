#!/bin/bash

# Test Spatial Queries - Verification Tool
# This script runs test queries to verify that the spatial data and operations work correctly

set -e  # Exit on any error

# Get script directory and load configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
source "$PROJECT_ROOT/config/data_sources.conf"

echo "=== Spatial Query Test Suite ==="
echo ""

# Check if data is prepared
if [ ! -f "$PROJECT_ROOT/data/processed/parquet/npl_files/grundnutzung.parquet" ]; then
    echo "Error: Prepared data not found!"
    echo "Please run the preparation script first:"
    echo "  ./raw_input_data/prepare_data.sh"
    echo ""
    exit 1
fi

echo "Running test queries to verify spatial operations..."
echo ""

# Test 1: Basic data availability
echo "Test 1: Checking data availability..."
duckdb -c "
INSTALL spatial;
LOAD spatial;

SELECT 'NPL Grundnutzung records: ' || COUNT(*) AS test_result
FROM 'data/processed/parquet/npl_files/grundnutzung.parquet';

SELECT 'SwissBoundaries municipalities: ' || COUNT(*) AS test_result
FROM st_read('data/input/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg', layer='tlm_hoheitsgebiet');
"

echo ""
echo "Test 2: Testing spatial intersection with known municipality (Steinhausen)..."
duckdb -c "
INSTALL spatial;
LOAD spatial;

SELECT
    zones.typ_kommunal_bezeichnung AS zone_name,
    COUNT(*) AS feature_count
FROM
    'data/processed/parquet/npl_files/grundnutzung.parquet' AS zones
JOIN
    st_read('data/input/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg', layer='tlm_hoheitsgebiet') AS municipalities
ON
    ST_Intersects(zones.geom, municipalities.geom)
WHERE
    municipalities.name = 'Steinhausen'
GROUP BY
    zones.typ_kommunal_bezeichnung
ORDER BY
    feature_count DESC
LIMIT 5;
"

echo ""
echo "Test 3: Testing canton-level aggregation..."
duckdb -c "
INSTALL spatial;
LOAD spatial;

SELECT
    kanton,
    COUNT(*) AS total_features
FROM
    'data/processed/parquet/npl_files/grundnutzung.parquet'
GROUP BY
    kanton
ORDER BY
    total_features DESC
LIMIT 10;
"

echo ""
echo "Test 4: Verifying geometry validity..."
duckdb -c "
INSTALL spatial;
LOAD spatial;

SELECT
    'Valid geometries: ' || COUNT(*) AS test_result
FROM
    'data/processed/parquet/npl_files/grundnutzung.parquet'
WHERE
    ST_IsValid(geom) = true
LIMIT 1;
"

echo ""
echo "=== Test Suite Complete ==="
echo ""
echo "If all tests completed without errors, your spatial data setup is working correctly!"
echo ""
echo "You can now run operational queries:"
echo "  ./operations/query_municipality_zones.sh Steinhausen"
echo "  ./operations/discover_municipalities.sh search Stein"
echo ""
