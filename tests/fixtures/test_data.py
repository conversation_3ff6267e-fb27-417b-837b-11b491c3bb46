"""
Test data fixtures and utilities.
"""

import pytest
import duckdb
from unittest.mock import Mock
from app.models.municipality import Municipality, MunicipalityBounds
from app.models.zone import Zone, ZoneType


@pytest.fixture
def sample_municipality():
    """Sample municipality for testing."""
    return Municipality(
        id="test-municipality",
        name="Test Municipality",
        canton="CH",
        bfs_code="1234",
        historical_code="5678"
    )


@pytest.fixture
def sample_municipality_bounds():
    """Sample municipality bounds for testing."""
    return MunicipalityBounds(
        min_x=8.5,
        min_y=47.0,
        max_x=8.6,
        max_y=47.1
    )


@pytest.fixture
def sample_zone():
    """Sample zone for testing."""
    return Zone(
        id="12345",
        zone_type=ZoneType.GRUNDNUTZUNG,
        municipality_id="test-municipality",
        municipality_name="Test Municipality",
        zone_code="R1",
        zone_name="Residential Zone",
        description="Single family residential zone",
        area=10000.0,
        properties={
            "canton": "ZH",
            "kantonal_zone_name": "Wohnzone",
            "geometry": {
                "type": "Polygon",
                "coordinates": [[[8.5, 47.0], [8.6, 47.0], [8.6, 47.1], [8.5, 47.1], [8.5, 47.0]]]
            }
        }
    )


@pytest.fixture
def sample_geojson_feature():
    """Sample GeoJSON feature for testing."""
    return {
        "type": "Feature",
        "properties": {
            "id": "12345",
            "zone_name": "Residential Zone",
            "municipality_code": "test-municipality",
            "canton": "ZH",
            "area": 10000.0,
            "zone_type": "grundnutzung"
        },
        "geometry": {
            "type": "Polygon",
            "coordinates": [[[8.5, 47.0], [8.6, 47.0], [8.6, 47.1], [8.5, 47.1], [8.5, 47.0]]]
        }
    }


@pytest.fixture
def sample_geojson_collection(sample_geojson_feature):
    """Sample GeoJSON FeatureCollection for testing."""
    return {
        "type": "FeatureCollection",
        "features": [sample_geojson_feature],
        "properties": {
            "municipality": "test-municipality",
            "zone_type": "grundnutzung",
            "feature_count": 1
        }
    }


@pytest.fixture
def mock_database():
    """Mock database connection for testing."""
    db = Mock(spec=duckdb.DuckDBPyConnection)
    return db


@pytest.fixture
def sample_swiss_coordinates():
    """Sample Swiss coordinates for testing."""
    return {
        "lv95": {
            "x": 2683141.0,  # Swiss LV95 coordinates
            "y": 1247637.0
        },
        "wgs84": {
            "lng": 8.5,  # WGS84 coordinates
            "lat": 47.0
        }
    }
