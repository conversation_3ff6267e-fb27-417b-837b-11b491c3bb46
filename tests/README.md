# Tests for Municipality Zones Viewer

This directory contains comprehensive tests for the Municipality Zones Viewer application.

## Test Structure

```
tests/
├── conftest.py                    # Shared test configuration and fixtures
├── fixtures/
│   └── test_data.py              # Test data fixtures and utilities
├── test_api/                     # API endpoint tests
│   ├── test_municipalities.py    # Municipality API tests
│   └── test_zones.py            # Zone API tests
├── test_services/               # Service layer tests
│   ├── test_municipality_service.py
│   ├── test_spatial_service.py
│   └── test_zone_service.py
└── test_integration/            # Integration tests
    └── test_main_functionality.py
```

## Test Categories

### Unit Tests (`test_services/`)
- Test individual service methods in isolation
- Use mocked database connections
- Fast execution
- Marked with `@pytest.mark.unit`

### API Tests (`test_api/`)
- Test FastAPI endpoints
- Use TestClient for HTTP requests
- Test both JSON and HTML responses
- Verify error handling

### Integration Tests (`test_integration/`)
- Test complete workflows
- Use real database connections (in-memory)
- Test data file loading and spatial operations
- Marked with `@pytest.mark.integration`

## Running Tests

### Using the Test Runner Script
```bash
# Run all tests
./scripts/run_tests.sh all

# Run only unit tests
./scripts/run_tests.sh unit

# Run only integration tests
./scripts/run_tests.sh integration

# Run with coverage
./scripts/run_tests.sh coverage

# Run fast tests (exclude slow tests)
./scripts/run_tests.sh fast
```

### Using pytest directly
```bash
# Activate virtual environment
source .venv/bin/activate

# Run all tests
pytest tests/

# Run specific test categories
pytest tests/ -m "unit"
pytest tests/ -m "integration"
pytest tests/ -m "not slow"

# Run with verbose output
pytest tests/ -v

# Run with coverage
pytest tests/ --cov=app --cov-report=html
```

## Test Configuration

### Environment Variables
Tests use the following environment variables (set automatically):
- `DEBUG=true`
- `DUCKDB_PATH=:memory:`
- `DATA_DIRECTORY=data/processed`
- `BOUNDARIES_DIRECTORY=data/boundaries`

### Pytest Configuration
Configuration is in `pyproject.toml`:
- Async mode: auto
- Test discovery: `test_*.py` files
- Markers: unit, integration, slow

### Fixtures
Common fixtures are available in `conftest.py`:
- `test_client`: FastAPI TestClient
- `mock_db_connection`: Mocked database
- `isolated_db`: In-memory database for integration tests

## Test Data

### Sample Data
Test fixtures provide sample data for:
- Municipalities (Swiss format)
- Zones (Grundnutzung, Überlagernde Flächen)
- GeoJSON features and collections
- Swiss coordinate systems (LV95, WGS84)

### Mock Database Responses
Service tests use mocked database responses that match the expected data structure from real DuckDB queries.

## Writing New Tests

### Unit Tests
```python
import pytest
from unittest.mock import Mock
from app.services.your_service import YourService

@pytest.mark.asyncio
async def test_your_method(mock_db_connection):
    # Setup mock
    mock_db_connection.execute.return_value.fetchall.return_value = [
        ("test_data",)
    ]
    
    # Test
    service = YourService(db=mock_db_connection)
    result = await service.your_method()
    
    # Assert
    assert len(result) == 1
```

### API Tests
```python
def test_your_endpoint(test_client):
    response = test_client.get("/api/your-endpoint/")
    assert response.status_code == 200
    data = response.json()
    assert "expected_field" in data
```

### Integration Tests
```python
@pytest.mark.integration
def test_complete_workflow(isolated_db):
    # Test with real database operations
    # This will use an in-memory DuckDB instance
    pass
```

## Continuous Integration

Tests are designed to run in CI environments:
- No external dependencies required
- Use in-memory databases
- Mock spatial data when files aren't available
- Graceful handling of missing data files

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure virtual environment is activated
2. **Database Errors**: Check that DuckDB spatial extension loads correctly
3. **Async Errors**: Make sure async tests use `@pytest.mark.asyncio`
4. **Mock Errors**: Verify mock return values match expected data structure

### Debug Mode
Run tests with verbose output and stop on first failure:
```bash
pytest tests/ -v -x --tb=short
```
