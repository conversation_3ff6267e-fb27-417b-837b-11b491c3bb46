"""
Tests for ÖREB service.
"""

import pytest
import json
from unittest.mock import Mock, AsyncMock, patch
import httpx
import xml.etree.ElementTree as ET
from fastapi import HTTPException

from app.services.oereb_service import OEREBService


@pytest.fixture
def oereb_service():
    """ÖREB service instance."""
    return OEREBService()


@pytest.fixture
def mock_egrid_response():
    """Mock response from maps.geo.admin.ch API."""
    return {
        "results": [
            {
                "layerBodId": "ch.swisstopo-vd.amtliche-vermessung",
                "layerName": "OpenData-AV",
                "featureId": 745913,
                "id": 745913,
                "attributes": {
                    "bfsnr": 1711,
                    "ak": "ZG",
                    "name": "4103",
                    "number": "4103",
                    "identnd": "ZG0200001711",
                    "egris_egrid": "CH607465170666",
                    "realestate_type": None,
                    "geoportal_url": "https://zugmap.ch/bmcl/",
                    "label": "4103"
                }
            }
        ]
    }


@pytest.fixture
def mock_zurich_egrid_response():
    """Mock response from maps.geo.admin.ch API for Zurich."""
    return {
        "results": [
            {
                "layerBodId": "ch.swisstopo-vd.amtliche-vermessung",
                "layerName": "OpenData-AV",
                "featureId": 2355706,
                "id": 2355706,
                "attributes": {
                    "bfsnr": 261,
                    "ak": "ZH",
                    "name": "AU287",
                    "number": "AU287",
                    "identnd": "ZH0200000261",
                    "egris_egrid": "CH379178299960",
                    "realestate_type": None,
                    "geoportal_url": "https://maps.zh.ch?topic=AVfarbigZH&locate=gemeinden&locations=261",
                    "label": "AU287"
                }
            }
        ]
    }


@pytest.fixture
def mock_empty_egrid_response():
    """Mock empty response from maps.geo.admin.ch API."""
    return {"results": []}


@pytest.fixture
def mock_oereb_xml():
    """Mock ÖREB XML response using v2.0 namespaces."""
    return """<?xml version="1.0" encoding="UTF-8"?>
<data:GetExtractByIdResponse xmlns:data="http://schemas.geo.admin.ch/V_D/OeREB/2.0/ExtractData">
    <data:Extract>
        <data:RealEstate>
            <data:EGRID>CH607465170666</data:EGRID>
            <data:MunicipalityName>Zug</data:MunicipalityName>
            <data:LandRegistryArea>1000</data:LandRegistryArea>
            <data:Number>4103</data:Number>
            <data:Type>
                <data:LocalisedText>
                    <data:Language>de</data:Language>
                    <data:Text>Liegenschaft</data:Text>
                </data:LocalisedText>
            </data:Type>
        </data:RealEstate>
        <data:RestrictionOnLandownership>
            <data:Theme>
                <data:LocalisedText>
                    <data:Language>de</data:Language>
                    <data:Text>Nutzungsplanung</data:Text>
                </data:LocalisedText>
            </data:Theme>
            <data:LegendText>
                <data:LocalisedText>
                    <data:Language>de</data:Language>
                    <data:Text>Wohnzone</data:Text>
                </data:LocalisedText>
            </data:LegendText>
            <data:TypeCode>N110</data:TypeCode>
            <data:LawStatus>inKraft</data:LawStatus>
            <data:AreaShare>500</data:AreaShare>
            <data:PartInPercent>50</data:PartInPercent>
        </data:RestrictionOnLandownership>
        <data:LegalProvision>
            <data:Title>
                <data:LocalisedText>
                    <data:Language>de</data:Language>
                    <data:Text>Baugesetz</data:Text>
                </data:LocalisedText>
            </data:Title>
            <data:Abbreviation>BauG</data:Abbreviation>
            <data:OfficialNumber>BGS 721.0</data:OfficialNumber>
            <data:TextAtWeb>https://example.com/baugesetz</data:TextAtWeb>
        </data:LegalProvision>
        <data:Document>
            <data:Title>
                <data:LocalisedText>
                    <data:Language>de</data:Language>
                    <data:Text>Zonenplan</data:Text>
                </data:LocalisedText>
            </data:Title>
            <data:Type>Rechtsvorschrift</data:Type>
            <data:OfficialNumber>ZP-2023-01</data:OfficialNumber>
            <data:TextAtWeb>https://example.com/zonenplan</data:TextAtWeb>
        </data:Document>
    </data:Extract>
</data:GetExtractByIdResponse>"""


class TestOEREBService:
    """Test cases for ÖREB service."""

    @pytest.mark.asyncio
    async def test_get_egrid_from_coordinates_success(self, oereb_service, mock_egrid_response):
        """Test successful EGRID lookup."""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.json.return_value = mock_egrid_response
            mock_response.raise_for_status.return_value = None

            mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)

            result = await oereb_service.get_egrid_from_coordinates(2679965.9, 1225908.5)

            assert result is not None
            assert result['egrid'] == 'CH607465170666'
            assert result['canton'] == 'ZG'
            assert result['parcel_number'] == '4103'
            assert result['bfs_number'] == 1711
            assert result['coordinates']['x'] == 2679965.9
            assert result['coordinates']['y'] == 1225908.5

    @pytest.mark.asyncio
    async def test_get_egrid_from_coordinates_zurich(self, oereb_service, mock_zurich_egrid_response):
        """Test EGRID lookup for Zurich coordinates."""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.json.return_value = mock_zurich_egrid_response
            mock_response.raise_for_status.return_value = None

            mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)

            result = await oereb_service.get_egrid_from_coordinates(2682302.4, 1247858.0)

            assert result is not None
            assert result['egrid'] == 'CH379178299960'
            assert result['canton'] == 'ZH'
            assert result['parcel_number'] == 'AU287'
            assert result['bfs_number'] == 261

    @pytest.mark.asyncio
    async def test_get_egrid_from_coordinates_no_property(self, oereb_service, mock_empty_egrid_response):
        """Test EGRID lookup when no property found."""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.json.return_value = mock_empty_egrid_response
            mock_response.raise_for_status.return_value = None

            mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)

            result = await oereb_service.get_egrid_from_coordinates(2556391.6, 1197551.7)

            assert result is None

    @pytest.mark.asyncio
    async def test_get_egrid_from_coordinates_timeout(self, oereb_service):
        """Test EGRID lookup timeout handling."""
        with patch('httpx.AsyncClient') as mock_client:
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(
                side_effect=httpx.TimeoutException("Request timeout")
            )

            with pytest.raises(HTTPException) as exc_info:
                await oereb_service.get_egrid_from_coordinates(2679965.9, 1225908.5)

            assert exc_info.value.status_code == 408
            assert "Zeitüberschreitung" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_get_egrid_from_coordinates_http_error(self, oereb_service):
        """Test EGRID lookup HTTP error handling."""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.raise_for_status.side_effect = httpx.HTTPStatusError(
                "Server error", request=Mock(), response=Mock()
            )

            mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)

            with pytest.raises(HTTPException) as exc_info:
                await oereb_service.get_egrid_from_coordinates(2679965.9, 1225908.5)

            assert exc_info.value.status_code == 502
            assert "Fehler beim Abrufen" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_get_oereb_data_success(self, oereb_service, mock_oereb_xml):
        """Test successful ÖREB data retrieval."""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.text = mock_oereb_xml
            mock_response.raise_for_status.return_value = None

            mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)

            result = await oereb_service.get_oereb_data('CH607465170666', 'ZG')

            assert result is not None
            assert result['egrid'] == 'CH607465170666'
            assert 'property_info' in result
            assert 'restrictions' in result
            assert 'legal_provisions' in result
            assert 'documents' in result

            # Check property info
            assert result['property_info']['egrid'] == 'CH607465170666'
            assert result['property_info']['municipality'] == 'Zug'
            assert result['property_info']['area'] == '1000'
            assert result['property_info']['type'] == 'Liegenschaft'
            assert result['property_info']['number'] == '4103'

            # Check restrictions
            assert len(result['restrictions']) == 1
            restriction = result['restrictions'][0]
            assert restriction['topic'] == 'Nutzungsplanung'
            assert restriction['type_code'] == 'N110'
            assert restriction['legend_text'] == 'Wohnzone'  # Updated field name
            assert restriction['lawstatus'] == 'inKraft'
            assert restriction['area'] == '500'
            assert restriction['part_in_percent'] == '50'

            # Check legal provisions
            assert len(result['legal_provisions']) == 1
            provision = result['legal_provisions'][0]
            assert provision['title'] == 'Baugesetz'
            assert provision['abbreviation'] == 'BauG'
            assert provision['number'] == 'BGS 721.0'
            assert provision['text_at_web'] == 'https://example.com/baugesetz'

            # Check documents
            assert len(result['documents']) == 1
            document = result['documents'][0]
            assert document['title'] == 'Zonenplan'
            assert document['type'] == 'Rechtsvorschrift'
            assert document['official_number'] == 'ZP-2023-01'
            assert document['web_reference'] == 'https://example.com/zonenplan'

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_real_oereb_data_zug(self, oereb_service):
        """Test with real Zug ÖREB data (integration test)."""
        # This test makes actual API calls to verify the implementation works end-to-end
        result = await oereb_service.get_oereb_data('CH607465170666', 'ZG')

        assert result is not None
        assert result['egrid'] == 'CH607465170666'

        # Should have property information
        assert 'property_info' in result
        property_info = result['property_info']
        assert property_info['egrid'] == 'CH607465170666'
        assert property_info['municipality'] == 'Zug'
        assert property_info['area'] == '333'  # Real area from Zug service
        assert property_info['type'] == 'Liegenschaft'

        # Should have restrictions
        assert 'restrictions' in result
        assert len(result['restrictions']) >= 1  # At least one restriction

        # Check first restriction (should be Wohnzone 2a)
        restriction = result['restrictions'][0]
        assert restriction['topic'] == 'Nutzungsplanung (kantonal/kommunal)'
        assert restriction['legend_text'] == 'Wohnzone 2a'
        assert restriction['type_code'] == '110202'

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_real_oereb_data_zurich(self, oereb_service):
        """Test with real Zurich ÖREB data (integration test)."""
        # This test makes actual API calls to verify the implementation works end-to-end
        result = await oereb_service.get_oereb_data('CH379178299960', 'ZH')

        assert result is not None
        assert result['egrid'] == 'CH379178299960'

        # Should have property information
        assert 'property_info' in result
        property_info = result['property_info']
        assert property_info['egrid'] == 'CH379178299960'
        assert property_info['municipality'] == 'Zürich'
        assert property_info['area'] == '246'  # Real area from Zurich service
        assert property_info['type'] == 'Liegenschaft'

        # Should have restrictions
        assert 'restrictions' in result
        assert len(result['restrictions']) >= 1  # At least one restriction

    @pytest.mark.asyncio
    async def test_get_oereb_data_invalid_canton(self, oereb_service):
        """Test ÖREB data retrieval with invalid canton."""
        with pytest.raises(HTTPException) as exc_info:
            await oereb_service.get_oereb_data('CH607465170666', 'XX')

        assert exc_info.value.status_code == 404
        assert "ÖREB-Service für Kanton XX nicht verfügbar" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_get_oereb_data_not_found(self, oereb_service):
        """Test ÖREB data retrieval when data not found."""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.raise_for_status.side_effect = httpx.HTTPStatusError(
                "Not found", request=Mock(), response=Mock(status_code=404)
            )

            mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)

            result = await oereb_service.get_oereb_data('CH607465170666', 'ZG')

            assert result is None

    @pytest.mark.asyncio
    async def test_get_oereb_data_timeout(self, oereb_service):
        """Test ÖREB data retrieval timeout handling."""
        with patch('httpx.AsyncClient') as mock_client:
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(
                side_effect=httpx.TimeoutException("Request timeout")
            )

            with pytest.raises(HTTPException) as exc_info:
                await oereb_service.get_oereb_data('CH607465170666', 'ZG')

            assert exc_info.value.status_code == 408
            assert "Zeitüberschreitung beim Abrufen der ÖREB-Daten" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_parse_oereb_xml_invalid(self, oereb_service):
        """Test XML parsing with invalid XML."""
        invalid_xml = "This is not valid XML"

        with pytest.raises(HTTPException) as exc_info:
            await oereb_service._parse_oereb_xml(invalid_xml, 'CH607465170666')

        assert exc_info.value.status_code == 502
        assert exc_info.value.detail == "Fehler beim Verarbeiten der ÖREB-Daten"

    def test_cantonal_services_available(self, oereb_service):
        """Test that all expected cantonal services are configured."""
        expected_cantons = [
            'AG', 'AR', 'AI', 'BE', 'BL', 'BS', 'FR', 'GE', 'GL', 'GR', 'JU', 'LU',
            'NW', 'OW', 'SH', 'SZ', 'SO', 'SG', 'TI', 'TG', 'UR', 'VD', 'VS', 'ZG', 'ZH'
        ]

        for canton in expected_cantons:
            assert canton in oereb_service.CANTONAL_OEREB_SERVICES
            assert oereb_service.CANTONAL_OEREB_SERVICES[canton].startswith('https://')

    def test_timeout_configuration(self, oereb_service):
        """Test that timeout is properly configured."""
        assert oereb_service.timeout == 30.0
