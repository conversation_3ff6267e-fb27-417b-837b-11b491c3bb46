"""
Tests for municipality service.
"""

import pytest
from unittest.mock import Mock, AsyncMock
from app.services.municipality_service import MunicipalityService
from app.models.municipality import Municipality, MunicipalityBounds


@pytest.fixture
def mock_db():
    """Mock database connection."""
    db = Mock()
    return db


@pytest.fixture
def municipality_service(mock_db):
    """Municipality service with mocked database."""
    return MunicipalityService(db=mock_db)


@pytest.mark.asyncio
async def test_get_municipalities(municipality_service, mock_db):
    """Test getting municipalities."""
    # Mock database response
    mock_db.execute.return_value.fetchall.return_value = [
        ("Test Municipality", "test-municipality", "CH")
    ]

    # Test the service
    result = await municipality_service.get_municipalities()

    # Verify the result
    assert len(result) == 1
    assert isinstance(result[0], Municipality)
    assert result[0].name == "Test Municipality"
    assert result[0].id == "test-municipality"


@pytest.mark.asyncio
async def test_get_municipality_by_id(municipality_service, mock_db):
    """Test getting municipality by ID."""
    # Mock database response
    mock_db.execute.return_value.fetchone.return_value = (
        "Test Municipality", "test-municipality", "CH"
    )

    # Test the service
    result = await municipality_service.get_municipality_by_id("test-municipality")

    # Verify the result
    assert result is not None
    assert isinstance(result, Municipality)
    assert result.name == "Test Municipality"


@pytest.mark.asyncio
async def test_get_municipality_by_id_not_found(municipality_service, mock_db):
    """Test getting municipality by ID when not found."""
    # Mock database response
    mock_db.execute.return_value.fetchone.return_value = None

    # Test the service
    result = await municipality_service.get_municipality_by_id("nonexistent")

    # Verify the result
    assert result is None


@pytest.mark.asyncio
async def test_get_municipality_bounds(municipality_service, mock_db):
    """Test getting municipality bounds."""
    # Mock database response
    mock_db.execute.return_value.fetchone.return_value = (8.5, 47.0, 8.6, 47.1)

    # Test the service
    result = await municipality_service.get_municipality_bounds("test-municipality")

    # Verify the result
    assert result is not None
    assert isinstance(result, MunicipalityBounds)
    assert result.min_x == 8.5
    assert result.min_y == 47.0
    assert result.max_x == 8.6
    assert result.max_y == 47.1


@pytest.mark.asyncio
async def test_get_municipality_bounds_not_found(municipality_service, mock_db):
    """Test getting municipality bounds when not found."""
    # Mock database response
    mock_db.execute.return_value.fetchone.return_value = None

    # Test the service
    result = await municipality_service.get_municipality_bounds("nonexistent")

    # Verify the result
    assert result is None
