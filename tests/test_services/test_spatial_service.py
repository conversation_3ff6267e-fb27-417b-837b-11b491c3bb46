"""
Tests for spatial service.
"""

import pytest
import json
from unittest.mock import Mock
from app.services.spatial_service import SpatialService


@pytest.fixture
def mock_db():
    """Mock database connection."""
    db = Mock()
    return db


@pytest.fixture
def spatial_service(mock_db):
    """Spatial service with mocked database."""
    return SpatialService(db=mock_db)


@pytest.mark.asyncio
async def test_search_locations(spatial_service, mock_db):
    """Test location search."""
    # Mock database response
    mock_db.execute.return_value.fetchall.return_value = [
        ("Test Municipality", "municipality", 8.5, 47.0)
    ]

    # Test the service
    result = await spatial_service.search_locations("Test")

    # Verify the result
    assert len(result) == 1
    assert result[0]["name"] == "Test Municipality"
    assert result[0]["type"] == "municipality"
    assert result[0]["lng"] == 8.5
    assert result[0]["lat"] == 47.0


@pytest.mark.asyncio
async def test_get_zones_at_point(spatial_service, mock_db):
    """Test getting zones at a specific point."""
    # Mock database response
    mock_db.execute.return_value.fetchall.return_value = [
        ("Residential Zone", "test-municipality", "grundnutzung")
    ]

    # Test the service
    result = await spatial_service.get_zones_at_point(8.5, 47.0)

    # Verify the result
    assert len(result) == 1
    assert result[0]["zone_name"] == "Residential Zone"
    assert result[0]["municipality_code"] == "test-municipality"
    assert result[0]["zone_type"] == "grundnutzung"


@pytest.mark.asyncio
async def test_get_zones_in_bounds(spatial_service, mock_db):
    """Test getting zones within bounds."""
    # Mock database response - note the geometry is returned as a string
    geojson_geometry = '{"type":"Polygon","coordinates":[[[8.5,47.0],[8.6,47.0],[8.6,47.1],[8.5,47.1],[8.5,47.0]]]}'
    mock_db.execute.return_value.fetchall.return_value = [
        (1, "Residential Zone", "test-municipality", geojson_geometry)
    ]

    # Test the service
    result = await spatial_service.get_zones_in_bounds(8.5, 47.0, 8.6, 47.1)

    # Verify the result
    assert result["type"] == "FeatureCollection"
    assert len(result["features"]) == 1
    assert result["features"][0]["properties"]["zone_name"] == "Residential Zone"
    assert result["features"][0]["properties"]["id"] == 1  # ID should be integer, not string

    # The geometry should be parsed from JSON string
    geometry = result["features"][0]["geometry"]
    if isinstance(geometry, str):
        # If it's still a string, parse it
        import json
        geometry = json.loads(geometry)
    assert isinstance(geometry, dict)  # Should be parsed dict
    assert geometry["type"] == "Polygon"


@pytest.mark.asyncio
async def test_search_locations_empty_result(spatial_service, mock_db):
    """Test location search with no results."""
    # Mock empty database response
    mock_db.execute.return_value.fetchall.return_value = []

    # Test the service
    result = await spatial_service.search_locations("NonexistentPlace")

    # Verify the result
    assert len(result) == 0
    assert isinstance(result, list)


@pytest.mark.asyncio
async def test_get_zones_at_point_no_zones(spatial_service, mock_db):
    """Test getting zones at point with no zones found."""
    # Mock empty database response
    mock_db.execute.return_value.fetchall.return_value = []

    # Test the service
    result = await spatial_service.get_zones_at_point(0.0, 0.0)

    # Verify the result
    assert len(result) == 0
    assert isinstance(result, list)
