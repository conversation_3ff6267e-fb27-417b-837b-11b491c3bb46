"""
Tests for zone service.
"""

import pytest
import json
from unittest.mock import Mock
from app.services.zone_service import ZoneService
from app.models.zone import Zone, ZoneType


@pytest.fixture
def mock_db():
    """Mock database connection."""
    db = Mock()
    return db


@pytest.fixture
def zone_service(mock_db):
    """Zone service with mocked database."""
    return ZoneService(db=mock_db)


@pytest.mark.asyncio
async def test_get_zones_no_municipality(zone_service, mock_db):
    """Test getting zones without municipality filter returns empty list."""
    # Test the service
    result = await zone_service.get_zones()
    
    # Verify the result
    assert len(result) == 0
    assert isinstance(result, list)


@pytest.mark.asyncio
async def test_get_zones_with_municipality(zone_service, mock_db):
    """Test getting zones with municipality filter."""
    # Mock database response
    mock_db.execute.return_value.fetchall.return_value = [
        (12345, "grundnutzung", "test-municipality", "Residential Zone", "Wohnzone", "ZH", 10000.0)
    ]
    
    # Test the service
    result = await zone_service.get_zones(municipality_id="test-municipality")
    
    # Verify the result
    assert len(result) == 1
    assert isinstance(result[0], Zone)
    assert result[0].zone_type == ZoneType.GRUNDNUTZUNG
    assert result[0].municipality_id == "test-municipality"
    assert result[0].zone_name == "Residential Zone"


@pytest.mark.asyncio
async def test_get_zones_with_type_filter(zone_service, mock_db):
    """Test getting zones with type filter."""
    # Mock database response
    mock_db.execute.return_value.fetchall.return_value = [
        (12345, "ueberlagernde_flaechen", "test-municipality", "Protection Zone", "Schutzzone", "ZH", 5000.0)
    ]
    
    # Test the service
    result = await zone_service.get_zones(
        municipality_id="test-municipality", 
        zone_type=ZoneType.UEBERLAGERNDE_FLAECHEN
    )
    
    # Verify the result
    assert len(result) == 1
    assert isinstance(result[0], Zone)
    assert result[0].zone_type == ZoneType.UEBERLAGERNDE_FLAECHEN
    assert result[0].zone_name == "Protection Zone"


@pytest.mark.asyncio
async def test_get_zone_by_id(zone_service, mock_db):
    """Test getting zone by ID."""
    # Mock database response
    geojson_geometry = '{"type":"Polygon","coordinates":[[[8.5,47.0],[8.6,47.0],[8.6,47.1],[8.5,47.1],[8.5,47.0]]]}'
    mock_db.execute.return_value.fetchone.return_value = (
        12345, "grundnutzung", "test-municipality", "Residential Zone", "Wohnzone", "ZH", 10000.0, geojson_geometry
    )
    
    # Test the service
    result = await zone_service.get_zone_by_id("12345")
    
    # Verify the result
    assert result is not None
    assert isinstance(result, Zone)
    assert result.id == "12345"
    assert result.zone_type == ZoneType.GRUNDNUTZUNG
    assert result.zone_name == "Residential Zone"
    assert "geometry" in result.properties


@pytest.mark.asyncio
async def test_get_zone_by_id_not_found(zone_service, mock_db):
    """Test getting zone by ID when not found."""
    # Mock database response
    mock_db.execute.return_value.fetchone.return_value = None
    
    # Test the service
    result = await zone_service.get_zone_by_id("nonexistent")
    
    # Verify the result
    assert result is None


@pytest.mark.asyncio
async def test_get_municipality_zones_geojson(zone_service, mock_db):
    """Test getting zones as GeoJSON."""
    # Mock database response
    geojson_geometry = '{"type":"Polygon","coordinates":[[[8.5,47.0],[8.6,47.0],[8.6,47.1],[8.5,47.1],[8.5,47.0]]]}'
    mock_db.execute.return_value.fetchall.return_value = [
        (12345, "Residential Zone", "Wohnzone", "test-municipality", "ZH", geojson_geometry, 10000.0, 85.5)
    ]
    
    # Test the service
    result = await zone_service.get_municipality_zones_geojson("test-municipality")
    
    # Verify the result
    assert result["type"] == "FeatureCollection"
    assert len(result["features"]) == 1
    
    feature = result["features"][0]
    assert feature["type"] == "Feature"
    assert feature["properties"]["id"] == "12345"
    assert feature["properties"]["zone_name"] == "Residential Zone"
    assert feature["geometry"]["type"] == "Polygon"


@pytest.mark.asyncio
async def test_get_municipality_zones_geojson_empty(zone_service, mock_db):
    """Test getting zones as GeoJSON with no results."""
    # Mock empty database response
    mock_db.execute.return_value.fetchall.return_value = []
    
    # Test the service
    result = await zone_service.get_municipality_zones_geojson("nonexistent-municipality")
    
    # Verify the result
    assert result["type"] == "FeatureCollection"
    assert len(result["features"]) == 0
    assert result["properties"]["municipality"] == "nonexistent-municipality"
