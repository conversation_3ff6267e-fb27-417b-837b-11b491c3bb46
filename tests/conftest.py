"""
Shared test configuration and fixtures.
"""

import pytest
import os
import tempfile
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient

# Import the app and database manager
from app.main import app
from app.core.database import db_manager


@pytest.fixture(scope="session")
def test_client():
    """Create a test client for the FastAPI app."""
    return TestClient(app)


@pytest.fixture(scope="function")
def mock_db_connection():
    """Mock database connection for unit tests."""
    mock_conn = Mock()
    mock_conn.execute.return_value = Mock()
    return mock_conn


@pytest.fixture(scope="function")
def isolated_db():
    """Create an isolated in-memory database for testing."""
    # Use a temporary in-memory database for each test
    with patch.object(db_manager, '_connection', None):
        with patch.object(db_manager, '_setup_complete', False):
            # This will create a fresh in-memory database
            conn = db_manager.get_connection()
            yield conn
            # Cleanup
            db_manager.close()


@pytest.fixture(autouse=True)
def setup_test_environment():
    """Setup test environment variables."""
    # Set test-specific environment variables
    test_env = {
        'DEBUG': 'true',
        'DUCKDB_PATH': ':memory:',
        'DATA_DIRECTORY': 'data/processed',
        'BOUNDARIES_DIRECTORY': 'data/boundaries'
    }
    
    # Store original values
    original_env = {}
    for key, value in test_env.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value
    
    yield
    
    # Restore original values
    for key, original_value in original_env.items():
        if original_value is None:
            os.environ.pop(key, None)
        else:
            os.environ[key] = original_value


@pytest.fixture
def sample_municipality_data():
    """Sample municipality data for testing."""
    return [
        ("Test Municipality 1", "test-municipality-1", "CH"),
        ("Test Municipality 2", "test-municipality-2", "CH"),
        ("Zurich", "zurich", "CH"),
    ]


@pytest.fixture
def sample_zone_data():
    """Sample zone data for testing."""
    return [
        (12345, "grundnutzung", "test-municipality-1", "Residential Zone", "Wohnzone", "ZH", 10000.0),
        (12346, "grundnutzung", "test-municipality-1", "Commercial Zone", "Gewerbezone", "ZH", 5000.0),
        (12347, "ueberlagernde_flaechen", "test-municipality-1", "Protection Zone", "Schutzzone", "ZH", 2000.0),
    ]


@pytest.fixture
def sample_geojson_geometry():
    """Sample GeoJSON geometry for testing."""
    return '{"type":"Polygon","coordinates":[[[8.5,47.0],[8.6,47.0],[8.6,47.1],[8.5,47.1],[8.5,47.0]]]}'


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "unit: mark test as unit test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically."""
    for item in items:
        # Add integration marker to integration tests
        if "test_integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        
        # Add unit marker to service and model tests
        elif any(path in str(item.fspath) for path in ["test_services", "test_models"]):
            item.add_marker(pytest.mark.unit)
        
        # Add slow marker to tests that might be slow
        if any(keyword in item.name for keyword in ["database", "spatial", "geojson"]):
            item.add_marker(pytest.mark.slow)
