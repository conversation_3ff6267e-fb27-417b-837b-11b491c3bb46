"""
Integration tests for ÖREB coordinate transformation and end-to-end functionality.
"""

import pytest
import math
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient

from app.main import app
from app.services.oereb_service import OEREBService


@pytest.fixture
def client():
    """Test client for the FastAPI app."""
    return TestClient(app)


class TestOEREBCoordinateTransformation:
    """Test coordinate transformation and end-to-end ÖREB functionality."""

    def test_coordinate_transformation_accuracy(self):
        """Test that coordinate transformation maintains accuracy."""
        # Known Swiss coordinates in EPSG:2056 (LV95)
        test_coordinates = [
            (2679965.9, 1225908.5),  # Zug
            (2682302.4, 1247858.0),  # Zurich
            (2600000.0, 1200000.0),  # Central Switzerland
            (2556391.6, 1197551.7),  # Lake Neuchatel
        ]
        
        for x, y in test_coordinates:
            # Coordinates should be within valid Swiss bounds
            assert 2485000 <= x <= 2834000, f"X coordinate {x} out of Swiss bounds"
            assert 1075000 <= y <= 1296000, f"Y coordinate {y} out of Swiss bounds"
            
            # Test precision (should be sub-meter accuracy)
            assert isinstance(x, (int, float))
            assert isinstance(y, (int, float))

    @pytest.mark.integration
    def test_egrid_lookup_with_known_coordinates(self, client):
        """Test EGRID lookup with known valid Swiss coordinates."""
        # Test coordinates that are known to have properties
        test_cases = [
            {
                "x": 2679965.9,
                "y": 1225908.5,
                "expected_canton": "ZG",
                "description": "Zug city center"
            },
            {
                "x": 2682302.4,
                "y": 1247858.0,
                "expected_canton": "ZH",
                "description": "Zurich area"
            }
        ]
        
        for case in test_cases:
            response = client.post(
                f"/api/oereb/lookup?x={case['x']}&y={case['y']}",
                headers={"HX-Request": "true"}
            )
            
            assert response.status_code == 200, f"Failed for {case['description']}"
            content = response.text
            
            # Should either find property or show appropriate error
            if "ÖREB-Informationen" in content:
                # Property found - check canton if specified
                if case.get("expected_canton"):
                    assert case["expected_canton"] in content, f"Expected canton {case['expected_canton']} not found in response for {case['description']}"
            else:
                # Should show appropriate error message
                assert ("Kein Grundstück" in content or 
                       "Keine ÖREB-Daten" in content), f"Unexpected response for {case['description']}: {content[:200]}"

    @pytest.mark.integration
    def test_coordinates_in_water_no_property(self, client):
        """Test that coordinates in water bodies return no property found."""
        # Coordinates in Lake Neuchatel (should have no property)
        response = client.post(
            "/api/oereb/lookup?x=2556391.6&y=1197551.7",
            headers={"HX-Request": "true"}
        )
        
        assert response.status_code == 200
        content = response.text
        
        # Should indicate no property found
        assert "Kein Grundstück" in content

    def test_coordinate_precision_handling(self, client):
        """Test that the system handles coordinate precision correctly."""
        # Test with high precision coordinates
        response = client.post(
            "/api/oereb/lookup?x=2679965.123456789&y=1225908.987654321",
            headers={"HX-Request": "true"}
        )
        
        assert response.status_code == 200
        # Should handle precision without errors

    def test_coordinate_boundary_values(self, client):
        """Test coordinates at Swiss boundary limits."""
        # Test coordinates at the edges of Swiss coordinate system
        boundary_tests = [
            (2485000, 1075000),  # Southwest corner
            (2834000, 1296000),  # Northeast corner
            (2600000, 1200000),  # Central Switzerland
        ]
        
        for x, y in boundary_tests:
            response = client.post(
                f"/api/oereb/lookup?x={x}&y={y}",
                headers={"HX-Request": "true"}
            )
            
            # Should handle boundary coordinates without errors
            assert response.status_code == 200

    def test_invalid_coordinates_outside_switzerland(self, client):
        """Test coordinates outside Switzerland."""
        # Coordinates outside Swiss bounds
        invalid_coordinates = [
            (0, 0),  # Origin
            (2400000, 1000000),  # West of Switzerland
            (3000000, 1400000),  # East of Switzerland
            (2600000, 900000),   # South of Switzerland
            (2600000, 1400000),  # North of Switzerland
        ]
        
        for x, y in invalid_coordinates:
            response = client.post(
                f"/api/oereb/lookup?x={x}&y={y}",
                headers={"HX-Request": "true"}
            )
            
            # Should handle gracefully (likely no property found)
            assert response.status_code == 200
            content = response.text
            assert "Kein Grundstück" in content or "ÖREB-Informationen" in content

    @pytest.mark.asyncio
    async def test_egrid_api_response_format(self):
        """Test that the EGRID API response format is handled correctly."""
        service = OEREBService()
        
        # Mock a typical response from maps.geo.admin.ch
        mock_response_data = {
            "results": [
                {
                    "layerBodId": "ch.swisstopo-vd.amtliche-vermessung",
                    "layerName": "OpenData-AV",
                    "featureId": 745913,
                    "id": 745913,
                    "attributes": {
                        "bfsnr": 1711,
                        "ak": "ZG",
                        "name": "4103",
                        "number": "4103",
                        "identnd": "ZG0200001711",
                        "egris_egrid": "CH607465170666",
                        "realestate_type": None,
                        "geoportal_url": "https://zugmap.ch/bmcl/",
                        "label": "4103"
                    }
                }
            ]
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.json.return_value = mock_response_data
            mock_response.raise_for_status.return_value = None
            
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)
            
            result = await service.get_egrid_from_coordinates(2679965.9, 1225908.5)
            
            assert result is not None
            assert result['egrid'] == 'CH607465170666'
            assert result['canton'] == 'ZG'
            assert result['parcel_number'] == '4103'
            assert result['bfs_number'] == 1711

    @pytest.mark.asyncio
    async def test_egrid_api_multiple_results(self):
        """Test handling of multiple results from EGRID API."""
        service = OEREBService()
        
        # Mock response with multiple properties (common in dense areas)
        mock_response_data = {
            "results": [
                {
                    "layerBodId": "ch.swisstopo-vd.amtliche-vermessung",
                    "layerName": "OpenData-AV",
                    "featureId": 745913,
                    "attributes": {
                        "bfsnr": 1711,
                        "ak": "ZG",
                        "name": "4103",
                        "number": "4103",
                        "egris_egrid": "CH607465170666"
                    }
                },
                {
                    "layerBodId": "ch.swisstopo-vd.amtliche-vermessung",
                    "layerName": "OpenData-AV",
                    "featureId": 745914,
                    "attributes": {
                        "bfsnr": 1711,
                        "ak": "ZG",
                        "name": "4104",
                        "number": "4104",
                        "egris_egrid": "CH607465170667"
                    }
                }
            ]
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.json.return_value = mock_response_data
            mock_response.raise_for_status.return_value = None
            
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)
            
            result = await service.get_egrid_from_coordinates(2679965.9, 1225908.5)
            
            # Should return the first valid result
            assert result is not None
            assert result['egrid'] == 'CH607465170666'
            assert result['parcel_number'] == '4103'

    def test_api_request_parameters(self):
        """Test that API request parameters are correctly formatted."""
        service = OEREBService()
        
        # Test coordinate formatting
        x, y = 2679965.9, 1225908.5
        
        expected_params = {
            'geometry': f'{x},{y}',
            'geometryType': 'esriGeometryPoint',
            'layers': 'all:ch.swisstopo-vd.amtliche-vermessung',
            'tolerance': 1,
            'returnGeometry': False,
            'mapExtent': f'{x-10},{y-10},{x+10},{y+10}',
            'imageDisplay': '1,1,96',
            'sr': 2056,
            'f': 'json'
        }
        
        # Verify parameter format
        assert expected_params['geometry'] == '2679965.9,1225908.5'
        assert expected_params['mapExtent'] == '2679955.9,1225898.5,2679975.9,1225918.5'
        assert expected_params['sr'] == 2056  # Swiss LV95 coordinate system

    @pytest.mark.integration
    def test_end_to_end_oereb_workflow(self, client):
        """Test complete end-to-end ÖREB workflow."""
        # Test the complete workflow from map click to ÖREB display
        
        # Step 1: Simulate map click with Swiss coordinates
        response = client.post(
            "/api/oereb/lookup?x=2679965.9&y=1225908.5",
            headers={"HX-Request": "true"}
        )
        
        assert response.status_code == 200
        content = response.text
        
        # Step 2: Verify response structure
        assert "oereb-data-container" in content or "oereb-error-container" in content
        
        # Step 3: Check for proper German language interface
        assert "ÖREB-Informationen" in content
        
        # Step 4: Verify coordinate display
        assert "2679965.9" in content
        assert "1225908.5" in content
        assert "EPSG:2056" in content

    def test_error_handling_robustness(self, client):
        """Test that error handling is robust across different scenarios."""
        error_test_cases = [
            {
                "x": "invalid",
                "y": "invalid",
                "expected_status": 422,
                "description": "Invalid coordinate format"
            },
            {
                "x": 999999999,
                "y": 999999999,
                "expected_status": 200,
                "description": "Coordinates far outside Switzerland"
            }
        ]
        
        for case in error_test_cases:
            response = client.post(
                f"/api/oereb/lookup?x={case['x']}&y={case['y']}",
                headers={"HX-Request": "true"}
            )
            
            assert response.status_code == case["expected_status"], f"Failed for {case['description']}"

    def test_performance_with_multiple_requests(self, client):
        """Test system performance with multiple concurrent-like requests."""
        # Test multiple requests to ensure system stability
        coordinates = [
            (2679965.9, 1225908.5),
            (2682302.4, 1247858.0),
            (2600000.0, 1200000.0),
        ]
        
        for x, y in coordinates:
            response = client.post(
                f"/api/oereb/lookup?x={x}&y={y}",
                headers={"HX-Request": "true"}
            )
            
            assert response.status_code == 200
            # Response should be reasonably fast (this is just a basic check)
            assert len(response.text) > 0
