"""
Integration tests for ÖREB API endpoints.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient
from fastapi import HTTPException

from app.main import app
from app.services.oereb_service import OEREBService


@pytest.fixture
def client():
    """Test client for the FastAPI app."""
    return TestClient(app)


@pytest.fixture
def mock_property_info():
    """Mock property information."""
    return {
        'egrid': 'CH607465170666',
        'municipality': 'Zug',
        'canton': 'ZG',
        'parcel_number': '4103',
        'bfs_number': 1711,
        'coordinates': {'x': 2679965.9, 'y': 1225908.5}
    }


@pytest.fixture
def mock_oereb_data():
    """Mock ÖREB data."""
    return {
        'egrid': 'CH607465170666',
        'property_info': {
            'egrid': 'CH607465170666',
            'municipality': 'Zug',
            'area': '1000',
            'type': 'Liegenschaft'
        },
        'restrictions': [
            {
                'topic': 'Nutzungsplanung',
                'type_code': 'N110',
                'legend_text': 'Wohnzone',
                'lawstatus': 'inKraft',
                'area': '500',
                'part_in_percent': '50'
            }
        ],
        'legal_provisions': [
            {
                'title': 'Baugesetz',
                'abbreviation': 'BauG',
                'number': 'BGS 721.0',
                'text_at_web': 'https://example.com/baugesetz'
            }
        ],
        'documents': [
            {
                'title': 'Zonenplan',
                'type': 'Rechtsvorschrift',
                'official_number': 'ZP-2023-01',
                'web_reference': 'https://example.com/zonenplan'
            }
        ]
    }


class TestOEREBAPI:
    """Test cases for ÖREB API endpoints."""

    def test_lookup_oereb_success_htmx(self, client, mock_property_info, mock_oereb_data):
        """Test successful ÖREB lookup with HTMX request."""
        with patch.object(OEREBService, 'get_egrid_from_coordinates', new_callable=AsyncMock) as mock_egrid:
            with patch.object(OEREBService, 'get_oereb_data', new_callable=AsyncMock) as mock_oereb:
                mock_egrid.return_value = mock_property_info
                mock_oereb.return_value = mock_oereb_data

                response = client.post(
                    "/api/oereb/lookup?x=2679965.9&y=1225908.5",
                    headers={"HX-Request": "true"}
                )

                assert response.status_code == 200
                assert "text/html" in response.headers["content-type"]

                # Check that the response contains expected content
                content = response.text
                assert "ÖREB-Informationen" in content
                assert "CH607465170666" in content
                assert "Zug" in content
                assert "ZG" in content
                assert "4103" in content
                assert "Nutzungsplanung" in content
                assert "Wohnzone" in content
                assert "Baugesetz" in content
                assert "Zonenplan" in content

    def test_lookup_oereb_success_json(self, client, mock_property_info, mock_oereb_data):
        """Test successful ÖREB lookup with JSON response."""
        with patch.object(OEREBService, 'get_egrid_from_coordinates', new_callable=AsyncMock) as mock_egrid:
            with patch.object(OEREBService, 'get_oereb_data', new_callable=AsyncMock) as mock_oereb:
                mock_egrid.return_value = mock_property_info
                mock_oereb.return_value = mock_oereb_data

                response = client.post("/api/oereb/lookup?x=2679965.9&y=1225908.5")

                assert response.status_code == 200
                assert "application/json" in response.headers["content-type"]

                data = response.json()
                assert "property_info" in data
                assert "oereb_data" in data
                assert "coordinates" in data
                assert data["property_info"]["egrid"] == "CH607465170666"
                assert data["oereb_data"]["egrid"] == "CH607465170666"

    def test_lookup_oereb_no_property_htmx(self, client):
        """Test ÖREB lookup when no property found with HTMX request."""
        with patch.object(OEREBService, 'get_egrid_from_coordinates', new_callable=AsyncMock) as mock_egrid:
            mock_egrid.return_value = None

            response = client.post(
                "/api/oereb/lookup?x=2556391.6&y=1197551.7",
                headers={"HX-Request": "true"}
            )

            assert response.status_code == 200
            assert "text/html" in response.headers["content-type"]

            content = response.text
            assert "Kein Grundstück gefunden" in content
            assert "no_property" in content or "Kein Grundstück an dieser Position gefunden" in content

    def test_lookup_oereb_no_property_json(self, client):
        """Test ÖREB lookup when no property found with JSON response."""
        with patch.object(OEREBService, 'get_egrid_from_coordinates', new_callable=AsyncMock) as mock_egrid:
            mock_egrid.return_value = None

            response = client.post("/api/oereb/lookup?x=2556391.6&y=1197551.7")

            assert response.status_code == 404
            assert "Kein Grundstück an dieser Position gefunden" in response.json()["detail"]

    def test_lookup_oereb_no_oereb_data_htmx(self, client, mock_property_info):
        """Test ÖREB lookup when no ÖREB data available with HTMX request."""
        with patch.object(OEREBService, 'get_egrid_from_coordinates', new_callable=AsyncMock) as mock_egrid:
            with patch.object(OEREBService, 'get_oereb_data', new_callable=AsyncMock) as mock_oereb:
                mock_egrid.return_value = mock_property_info
                mock_oereb.return_value = None

                response = client.post(
                    "/api/oereb/lookup?x=2679965.9&y=1225908.5",
                    headers={"HX-Request": "true"}
                )

                assert response.status_code == 200
                assert "text/html" in response.headers["content-type"]

                content = response.text
                assert "Keine ÖREB-Daten" in content
                assert "CH607465170666" in content

    def test_lookup_oereb_no_oereb_data_json(self, client, mock_property_info):
        """Test ÖREB lookup when no ÖREB data available with JSON response."""
        with patch.object(OEREBService, 'get_egrid_from_coordinates', new_callable=AsyncMock) as mock_egrid:
            with patch.object(OEREBService, 'get_oereb_data', new_callable=AsyncMock) as mock_oereb:
                mock_egrid.return_value = mock_property_info
                mock_oereb.return_value = None

                response = client.post("/api/oereb/lookup?x=2679965.9&y=1225908.5")

                assert response.status_code == 404
                assert "Keine ÖREB-Daten für Grundstück CH607465170666 verfügbar" in response.json()["detail"]

    def test_lookup_oereb_service_error_htmx(self, client):
        """Test ÖREB lookup with service error and HTMX request."""
        with patch.object(OEREBService, 'get_egrid_from_coordinates', new_callable=AsyncMock) as mock_egrid:
            mock_egrid.side_effect = HTTPException(status_code=408, detail="Zeitüberschreitung")

            response = client.post(
                "/api/oereb/lookup?x=2679965.9&y=1225908.5",
                headers={"HX-Request": "true"}
            )

            assert response.status_code == 408

    def test_lookup_oereb_unexpected_error_htmx(self, client):
        """Test ÖREB lookup with unexpected error and HTMX request."""
        with patch.object(OEREBService, 'get_egrid_from_coordinates', new_callable=AsyncMock) as mock_egrid:
            mock_egrid.side_effect = Exception("Unexpected error")

            response = client.post(
                "/api/oereb/lookup?x=2679965.9&y=1225908.5",
                headers={"HX-Request": "true"}
            )

            assert response.status_code == 200
            assert "text/html" in response.headers["content-type"]

            content = response.text
            assert "Unerwarteter Fehler" in content

    def test_lookup_oereb_missing_coordinates(self, client):
        """Test ÖREB lookup with missing coordinates."""
        response = client.post("/api/oereb/lookup")

        assert response.status_code == 422  # Validation error

    def test_lookup_oereb_invalid_coordinates(self, client):
        """Test ÖREB lookup with invalid coordinates."""
        response = client.post("/api/oereb/lookup?x=invalid&y=invalid")

        assert response.status_code == 422  # Validation error

    def test_get_oereb_by_egrid_success_htmx(self, client, mock_oereb_data):
        """Test getting ÖREB data by EGRID with HTMX request."""
        with patch.object(OEREBService, 'get_oereb_data', new_callable=AsyncMock) as mock_oereb:
            mock_oereb.return_value = mock_oereb_data

            response = client.get(
                "/api/oereb/egrid/CH607465170666?canton=ZG",
                headers={"HX-Request": "true"}
            )

            assert response.status_code == 200
            assert "text/html" in response.headers["content-type"]

            content = response.text
            assert "CH607465170666" in content
            assert "Nutzungsplanung" in content

    def test_get_oereb_by_egrid_success_json(self, client, mock_oereb_data):
        """Test getting ÖREB data by EGRID with JSON response."""
        with patch.object(OEREBService, 'get_oereb_data', new_callable=AsyncMock) as mock_oereb:
            mock_oereb.return_value = mock_oereb_data

            response = client.get("/api/oereb/egrid/CH607465170666?canton=ZG")

            assert response.status_code == 200
            assert "application/json" in response.headers["content-type"]

            data = response.json()
            assert "property_info" in data
            assert "oereb_data" in data
            assert data["property_info"]["egrid"] == "CH607465170666"
            assert data["property_info"]["canton"] == "ZG"

    def test_get_oereb_by_egrid_missing_canton(self, client):
        """Test getting ÖREB data by EGRID without canton."""
        response = client.get("/api/oereb/egrid/CH607465170666")

        assert response.status_code == 400
        assert "Kanton muss angegeben werden" in response.json()["detail"]

    def test_get_oereb_by_egrid_not_found_htmx(self, client):
        """Test getting ÖREB data by EGRID when not found with HTMX request."""
        with patch.object(OEREBService, 'get_oereb_data', new_callable=AsyncMock) as mock_oereb:
            mock_oereb.return_value = None

            response = client.get(
                "/api/oereb/egrid/CH607465170666?canton=ZG",
                headers={"HX-Request": "true"}
            )

            assert response.status_code == 200
            assert "text/html" in response.headers["content-type"]

            content = response.text
            assert "Keine ÖREB-Daten" in content

    def test_get_oereb_by_egrid_not_found_json(self, client):
        """Test getting ÖREB data by EGRID when not found with JSON response."""
        with patch.object(OEREBService, 'get_oereb_data', new_callable=AsyncMock) as mock_oereb:
            mock_oereb.return_value = None

            response = client.get("/api/oereb/egrid/CH607465170666?canton=ZG")

            assert response.status_code == 404
            assert "Keine ÖREB-Daten für Grundstück CH607465170666 verfügbar" in response.json()["detail"]

    def test_get_available_cantons(self, client):
        """Test getting available cantons."""
        response = client.get("/api/oereb/cantons")

        assert response.status_code == 200
        data = response.json()

        assert "cantons" in data
        assert "services" in data
        assert isinstance(data["cantons"], list)
        assert isinstance(data["services"], dict)

        # Check that all expected cantons are present
        expected_cantons = [
            'AG', 'AR', 'AI', 'BE', 'BL', 'BS', 'FR', 'GE', 'GL', 'GR', 'JU', 'LU',
            'NW', 'OW', 'SH', 'SZ', 'SO', 'SG', 'TI', 'TG', 'UR', 'VD', 'VS', 'ZG', 'ZH'
        ]

        for canton in expected_cantons:
            assert canton in data["cantons"]
            assert canton in data["services"]
            assert data["services"][canton].startswith("https://")

    def test_real_coordinates_zug(self, client):
        """Test with real Zug coordinates (integration test)."""
        # This test uses real coordinates and may make actual API calls
        # Mark as slow test
        response = client.post(
            "/api/oereb/lookup?x=2679965.9&y=1225908.5",
            headers={"HX-Request": "true"}
        )

        # Should get a response (either success or no ÖREB data)
        assert response.status_code == 200
        content = response.text
        # Should either show ÖREB data or "no data" message
        assert ("ÖREB-Informationen" in content or
                "Keine ÖREB-Daten" in content or
                "Kein Grundstück" in content)

    def test_real_coordinates_zurich(self, client):
        """Test with real Zurich coordinates (integration test)."""
        # This test uses real coordinates and may make actual API calls
        response = client.post(
            "/api/oereb/lookup?x=2682302.4&y=1247858.0",
            headers={"HX-Request": "true"}
        )

        # Should get a response (either success or no ÖREB data)
        assert response.status_code == 200
        content = response.text
        # Should either show ÖREB data or "no data" message
        assert ("ÖREB-Informationen" in content or
                "Keine ÖREB-Daten" in content or
                "Kein Grundstück" in content)

    def test_real_coordinates_lake_neuchatel(self, client):
        """Test with coordinates in Lake Neuchatel (should find no property)."""
        response = client.post(
            "/api/oereb/lookup?x=2556391.6&y=1197551.7",
            headers={"HX-Request": "true"}
        )

        # Should get a response indicating no property found
        assert response.status_code == 200
        content = response.text
        assert "Kein Grundstück" in content
