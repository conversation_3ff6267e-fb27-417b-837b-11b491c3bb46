"""
Tests for municipality API endpoints.
"""

import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)


def test_get_municipalities():
    """Test getting list of municipalities."""
    response = client.get("/api/municipalities/")
    assert response.status_code == 200
    data = response.json()
    assert "municipalities" in data
    assert isinstance(data["municipalities"], list)


def test_get_municipality_by_id():
    """Test getting a specific municipality."""
    # First get a real municipality to test with
    municipalities_response = client.get("/api/municipalities/")
    assert municipalities_response.status_code == 200
    municipalities_data = municipalities_response.json()

    if municipalities_data["municipalities"]:
        # Test with a real municipality
        municipality_id = municipalities_data["municipalities"][0]["id"]
        response = client.get(f"/api/municipalities/{municipality_id}")
        assert response.status_code == 200
        data = response.json()
        assert "id" in data
        assert "name" in data
        assert "canton" in data

    # Test with non-existent municipality
    response = client.get("/api/municipalities/nonexistent-municipality")
    # Could be 404 (not found) or 500 (database error if no data loaded)
    assert response.status_code in [404, 500]


def test_get_municipality_bounds():
    """Test getting municipality bounds."""
    # First get a real municipality to test with
    municipalities_response = client.get("/api/municipalities/")
    assert municipalities_response.status_code == 200
    municipalities_data = municipalities_response.json()

    if municipalities_data["municipalities"]:
        # Test with a real municipality
        municipality_id = municipalities_data["municipalities"][0]["id"]
        response = client.get(f"/api/municipalities/{municipality_id}/bounds")
        assert response.status_code in [200, 404]  # 404 is OK if no spatial data

        if response.status_code == 200:
            data = response.json()
            assert "min_x" in data
            assert "min_y" in data
            assert "max_x" in data
            assert "max_y" in data

    # Test with non-existent municipality
    response = client.get("/api/municipalities/nonexistent-municipality/bounds")
    # Could be 404 (not found) or 500 (database error if no data loaded)
    assert response.status_code in [404, 500]


def test_get_municipalities_with_canton_filter():
    """Test filtering municipalities by canton."""
    response = client.get("/api/municipalities/?canton=ZH")
    assert response.status_code == 200
    data = response.json()
    assert "municipalities" in data


def test_municipalities_endpoint_structure():
    """Test that municipalities endpoint returns proper structure."""
    response = client.get("/api/municipalities/")
    assert response.status_code == 200
    data = response.json()

    # Check structure
    assert "municipalities" in data
    assert isinstance(data["municipalities"], list)

    # If we have municipalities, check their structure
    if data["municipalities"]:
        municipality = data["municipalities"][0]
        assert "id" in municipality
        assert "name" in municipality
        assert "canton" in municipality
        assert isinstance(municipality["id"], str)
        assert isinstance(municipality["name"], str)
        assert isinstance(municipality["canton"], str)
