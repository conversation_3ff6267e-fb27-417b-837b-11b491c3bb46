"""
Tests for zone API endpoints.
"""

import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_get_zones():
    """Test getting list of zones."""
    response = client.get("/api/zones/")
    assert response.status_code == 200
    data = response.json()
    assert "zones" in data
    assert isinstance(data["zones"], list)

def test_get_zones_with_municipality_filter():
    """Test filtering zones by municipality."""
    response = client.get("/api/zones/?municipality=test-municipality")
    assert response.status_code == 200
    data = response.json()
    assert "zones" in data

def test_get_zones_with_type_filter():
    """Test filtering zones by type."""
    response = client.get("/api/zones/?zone_type=grundnutzung")
    assert response.status_code == 200
    data = response.json()
    assert "zones" in data

def test_get_zones_with_limit():
    """Test limiting number of zones returned."""
    response = client.get("/api/zones/?limit=10")
    assert response.status_code == 200
    data = response.json()
    assert "zones" in data
    assert len(data["zones"]) <= 10

def test_get_zone_by_id():
    """Test getting a specific zone."""
    # Test with a numeric ID (zones use hash-based numeric IDs)
    response = client.get("/api/zones/123456789")
    # Could be 200 (found), 404 (not found), or 500 (database error if no data loaded)
    assert response.status_code in [200, 404, 500]

    # Test with invalid ID format
    response = client.get("/api/zones/invalid-zone-id")
    # Could be 404 (not found) or 500 (database error if no data loaded)
    assert response.status_code in [404, 500]

def test_get_municipality_zones_geojson():
    """Test getting zones as GeoJSON."""
    response = client.get("/api/zones/municipality/test-municipality/geojson")
    assert response.status_code in [200, 404]

    if response.status_code == 200:
        data = response.json()
        assert "type" in data
        assert data["type"] == "FeatureCollection"
        assert "features" in data
