#!/bin/bash

# Municipality Zones Viewer - Setup Script
# This script sets up the development environment

set -e  # Exit on any error

echo "🏗️  Setting up Municipality Zones Viewer development environment..."

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Check Python version
echo "📋 Checking Python version..."
python3 --version

# Create virtual environment if it doesn't exist
if [ ! -d ".venv" ]; then
    echo "🐍 Creating virtual environment..."
    python3 -m venv .venv
else
    echo "✅ Virtual environment already exists"
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source .venv/bin/activate

# Upgrade pip
echo "📦 Upgrading pip..."
pip install --upgrade pip

# Install dependencies (main + development)
echo "📚 Installing dependencies..."
pip install -e .[dev]

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "⚙️  Creating .env file from template..."
    cp .env.example .env
    echo "📝 Please review and modify .env file as needed"
else
    echo "✅ .env file already exists"
fi

# Make scripts executable
echo "🔐 Making scripts executable..."
chmod +x start_server.sh
chmod +x setup.sh

echo ""
echo "🎉 Setup complete!"
echo ""
echo "To start the development server:"
echo "  ./start_server.sh"
echo ""
echo "To run tests:"
echo "  source .venv/bin/activate"
echo "  pytest"
echo ""
echo "To format code:"
echo "  source .venv/bin/activate"
echo "  black ."
echo ""
