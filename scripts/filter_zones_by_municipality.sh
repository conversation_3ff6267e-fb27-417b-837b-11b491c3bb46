#!/bin/bash

# Interactive script to filter zones by municipality using DuckDB
# This script queries the parquet files for zone information

# Load configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/load_config.sh"

echo "=== Zone Filter by Municipality/Canton ==="
echo "This script helps you find zones in specific municipalities or cantons."
echo ""

# Function to show available cantons
show_cantons() {
    echo "Available cantons in the dataset:"
    duckdb -c "
    INSTALL spatial;
    LOAD spatial;
    SELECT
        kanton,
        COUNT(*) AS feature_count,
        COUNT(DISTINCT typ_kommunal_bezeichnung) AS unique_kommunal_zones,
        COUNT(DISTINCT typ_kantonal_bezeichnung) AS unique_kantonal_zones
    FROM 'output_final/npl_files/grundnutzung.parquet'
    GROUP BY kanton
    ORDER BY kanton;"
    echo ""
}

# Function to filter zones by canton
filter_by_canton() {
    local canton=$1
    echo "=== Zones for Canton: $canton ==="
    echo ""

    echo "Summary:"
    duckdb -c "
    INSTALL spatial;
    LOAD spatial;
    SELECT
        'Total Features' AS metric,
        COUNT(*) AS count
    FROM 'output_final/npl_files/grundnutzung.parquet'
    WHERE kanton = '$canton'
    UNION ALL
    SELECT
        'Unique Kommunal Zones' AS metric,
        COUNT(DISTINCT typ_kommunal_bezeichnung) AS count
    FROM 'output_final/npl_files/grundnutzung.parquet'
    WHERE kanton = '$canton' AND typ_kommunal_bezeichnung IS NOT NULL
    UNION ALL
    SELECT
        'Unique Kantonal Zones' AS metric,
        COUNT(DISTINCT typ_kantonal_bezeichnung) AS count
    FROM 'output_final/npl_files/grundnutzung.parquet'
    WHERE kanton = '$canton' AND typ_kantonal_bezeichnung IS NOT NULL;"

    echo ""
    echo "Kommunal Zone Names (Local Municipality Zones):"
    duckdb -c "
    INSTALL spatial;
    LOAD spatial;
    SELECT
        typ_kommunal_bezeichnung AS zone_name,
        COUNT(*) AS feature_count
    FROM 'output_final/npl_files/grundnutzung.parquet'
    WHERE kanton = '$canton'
        AND typ_kommunal_bezeichnung IS NOT NULL
    GROUP BY typ_kommunal_bezeichnung
    ORDER BY typ_kommunal_bezeichnung;"

    echo ""
    echo "Kantonal Zone Names (Cantonal Standard Zones):"
    duckdb -c "
    INSTALL spatial;
    LOAD spatial;
    SELECT
        typ_kantonal_bezeichnung AS zone_name,
        COUNT(*) AS feature_count
    FROM 'output_final/npl_files/grundnutzung.parquet'
    WHERE kanton = '$canton'
        AND typ_kantonal_bezeichnung IS NOT NULL
    GROUP BY typ_kantonal_bezeichnung
    ORDER BY typ_kantonal_bezeichnung;"
}



# Function to search for zones by municipality using spatial intersection
search_municipality_spatial() {
    local municipality_name="$1"
    local canton_filter="$2"  # Optional canton filter

    echo "=== Zones in municipality: $municipality_name (using spatial intersection) ==="
    echo ""

    # Determine canton filter
    local canton_clause=""
    if [ -n "$canton_filter" ]; then
        if is_valid_canton "$canton_filter"; then
            canton_clause="AND zones.$NPL_CANTON_COLUMN = '$canton_filter'"
            echo "Filtering by canton: $canton_filter"
        else
            echo "Warning: Invalid canton code '$canton_filter'. Searching all cantons."
        fi
    else
        # Try to auto-detect canton from known municipalities
        local auto_canton=$(get_canton_from_municipality "$municipality_name")
        if [ -n "$auto_canton" ]; then
            canton_clause="AND zones.$NPL_CANTON_COLUMN = '$auto_canton'"
            echo "Auto-detected canton: $auto_canton"
        else
            echo "Searching all cantons (no canton filter specified)"
        fi
    fi

    # Build and execute query
    local query="
    $(get_duckdb_prefix)
    SELECT
        zones.$NPL_KOMMUNAL_ZONE_COLUMN AS zone_name,
        zones.$NPL_KANTONAL_ZONE_COLUMN AS kantonal_equivalent,
        COUNT(*) AS feature_count,
        zones.$NPL_CANTON_COLUMN AS canton
    FROM
        st_read('$NPL_GRUNDNUTZUNG_GPKG', layer='$NPL_GRUNDNUTZUNG_LAYER') AS zones
    JOIN
        st_read('$SWISSBOUNDARIES_GPKG', layer='$SWISSBOUNDARIES_MUNICIPALITY_LAYER') AS municipalities
    ON
        ST_Intersects(zones.$NPL_GEOMETRY_COLUMN, municipalities.$SB_GEOMETRY_COLUMN)
    WHERE
        municipalities.$SB_MUNICIPALITY_NAME_COLUMN = '$municipality_name'
        $canton_clause
    GROUP BY
        zones.$NPL_KOMMUNAL_ZONE_COLUMN, zones.$NPL_KANTONAL_ZONE_COLUMN, zones.$NPL_CANTON_COLUMN
    ORDER BY
        feature_count DESC
    LIMIT $MAX_RESULTS;"

    $(get_timeout_cmd) duckdb -c "$query"
}

# Function to search for specific municipality in zone names
search_municipality() {
    local municipality=$1
    echo "=== Searching for zones containing: $municipality ==="
    echo ""

    duckdb -c "
    INSTALL spatial;
    LOAD spatial;
    SELECT
        kanton,
        typ_kommunal_bezeichnung AS kommunal_zone,
        typ_kantonal_bezeichnung AS kantonal_zone,
        COUNT(*) AS feature_count
    FROM 'output_final/npl_files/grundnutzung.parquet'
    WHERE typ_kommunal_bezeichnung LIKE '%$municipality%'
        OR typ_kantonal_bezeichnung LIKE '%$municipality%'
        OR bemerkungen LIKE '%$municipality%'
    GROUP BY kanton, typ_kommunal_bezeichnung, typ_kantonal_bezeichnung
    ORDER BY kanton, feature_count DESC;"
}

# Main script logic
if [ $# -eq 0 ]; then
    echo "Usage: $0 [canton|search|spatial] [value] [canton_filter]"
    echo ""
    echo "Examples:"
    echo "  $0 canton ZG                    # Show all zones for Zug canton"
    echo "  $0 search Steinhausen           # Search for zones containing 'Steinhausen'"
    echo "  $0 spatial Steinhausen          # Show zones within Steinhausen (auto-detect canton)"
    echo "  $0 spatial Steinhausen ZG       # Show zones within Steinhausen, filter by ZG canton"
    echo "  $0 spatial Zurich ZH            # Show zones within Zurich municipality"
    echo "  $0 spatial Basel BS             # Show zones within Basel municipality"
    echo "  $0 cantons                      # Show all available cantons"
    echo ""
    echo "The 'spatial' option uses SwissBoundaries3D data to find zones within"
    echo "exact municipality boundaries via spatial intersection with DuckDB."
    echo ""
    echo "Available cantons: $AVAILABLE_CANTONS"
    echo ""
    show_cantons
elif [ "$1" = "cantons" ]; then
    show_cantons
elif [ "$1" = "canton" ] && [ -n "$2" ]; then
    filter_by_canton "$2"
elif [ "$1" = "search" ] && [ -n "$2" ]; then
    search_municipality "$2"
elif [ "$1" = "spatial" ] && [ -n "$2" ]; then
    search_municipality_spatial "$2" "$3"  # $3 is optional canton filter
else
    echo "Invalid arguments. Use '$0' without arguments to see usage."
fi
