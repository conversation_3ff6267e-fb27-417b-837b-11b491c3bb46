#!/bin/bash

# Load configuration from data_sources.conf
# This script sources the configuration file and provides helper functions

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/../config/data_sources.conf"

# Load configuration
if [ -f "$CONFIG_FILE" ]; then
    source "$CONFIG_FILE"
else
    echo "Error: Configuration file not found: $CONFIG_FILE"
    exit 1
fi

# Helper function to get canton from municipality name
get_canton_from_municipality() {
    local municipality_name="$1"
    echo "$KNOWN_MUNICIPALITIES" | grep -i "^$municipality_name:" | cut -d':' -f3 | head -1
}

# Helper function to get BFS code from municipality name
get_bfs_from_municipality() {
    local municipality_name="$1"
    echo "$KNOWN_MUNICIPALITIES" | grep -i "^$municipality_name:" | cut -d':' -f2 | head -1
}

# Helper function to validate canton code
is_valid_canton() {
    local canton="$1"
    echo "$AVAILABLE_CANTONS" | grep -q "\b$canton\b"
}

# Helper function to build DuckDB command prefix
get_duckdb_prefix() {
    echo "INSTALL $DUCKDB_EXTENSIONS; LOAD $DUCKDB_EXTENSIONS;"
}

# Helper function to get timeout command
get_timeout_cmd() {
    echo "timeout $SPATIAL_QUERY_TIMEOUT"
}
