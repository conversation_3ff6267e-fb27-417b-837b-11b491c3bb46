#!/bin/bash

# Environment Management Script
# Helps manage different environment configurations

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

show_help() {
    echo "Environment Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  dev        Set up development environment (.env)"
    echo "  prod       Set up production environment (.env.production)"
    echo "  test       Set up test environment (.env.test)"
    echo "  show       Show current environment variables"
    echo "  validate   Validate environment configuration"
    echo "  help       Show this help message"
    echo ""
}

setup_dev() {
    echo "🔧 Setting up development environment..."
    cd "$PROJECT_DIR"
    
    if [ ! -f ".env" ]; then
        cp .env.example .env
        echo "✅ Created .env from template"
        echo "📝 Please review and modify .env as needed"
    else
        echo "✅ .env already exists"
    fi
    
    # Set development-specific values
    sed -i 's/DEBUG=false/DEBUG=true/' .env
    echo "🐛 Enabled debug mode"
}

setup_prod() {
    echo "🚀 Setting up production environment..."
    cd "$PROJECT_DIR"
    
    if [ ! -f ".env.production" ]; then
        cp .env.example .env.production
        echo "✅ Created .env.production from template"
        
        # Set production-specific values
        sed -i 's/DEBUG=false/DEBUG=false/' .env.production
        sed -i 's/DUCKDB_PATH=:memory:/DUCKDB_PATH=data\/app.duckdb/' .env.production
        
        echo "📝 Please review and modify .env.production for production use"
        echo "⚠️  Remember to set secure values for production!"
    else
        echo "✅ .env.production already exists"
    fi
}

setup_test() {
    echo "🧪 Setting up test environment..."
    cd "$PROJECT_DIR"
    
    if [ ! -f ".env.test" ]; then
        cp .env.example .env.test
        echo "✅ Created .env.test from template"
        
        # Set test-specific values
        sed -i 's/DEBUG=false/DEBUG=true/' .env.test
        sed -i 's/DUCKDB_PATH=:memory:/DUCKDB_PATH=:memory:/' .env.test
        
        echo "🧪 Configured for testing with in-memory database"
    else
        echo "✅ .env.test already exists"
    fi
}

show_env() {
    echo "📋 Current environment variables:"
    cd "$PROJECT_DIR"
    
    if [ -f ".env" ]; then
        echo ""
        echo "From .env:"
        grep -v '^#' .env | grep -v '^$' || echo "  (no variables set)"
    else
        echo "  No .env file found"
    fi
}

validate_env() {
    echo "✅ Validating environment configuration..."
    cd "$PROJECT_DIR"
    
    if [ ! -f ".env" ]; then
        echo "❌ No .env file found"
        echo "   Run: $0 dev"
        exit 1
    fi
    
    # Check required variables
    required_vars=("APP_NAME" "DUCKDB_PATH" "DATA_DIRECTORY")
    
    for var in "${required_vars[@]}"; do
        if grep -q "^${var}=" .env; then
            echo "✅ $var is set"
        else
            echo "❌ $var is missing"
        fi
    done
    
    echo "✅ Environment validation complete"
}

case "${1:-help}" in
    dev)
        setup_dev
        ;;
    prod)
        setup_prod
        ;;
    test)
        setup_test
        ;;
    show)
        show_env
        ;;
    validate)
        validate_env
        ;;
    help|*)
        show_help
        ;;
esac
