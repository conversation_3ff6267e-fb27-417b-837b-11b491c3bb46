#!/bin/bash

# Universal spatial query script that works with any municipality/canton
# Usage: ./universal_spatial_query.sh [municipality_name] [optional_canton]

# Load configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/load_config.sh"

# Function to execute spatial query for any municipality
execute_spatial_query() {
    local municipality_name="$1"
    local canton_filter="$2"
    
    echo "=== Spatial Query: $municipality_name ==="
    echo ""
    
    # Build canton filter clause
    local canton_clause=""
    if [ -n "$canton_filter" ]; then
        canton_clause="AND zones.$NPL_CANTON_COLUMN = '$canton_filter'"
        echo "Canton filter: $canton_filter"
    else
        echo "No canton filter (searching all cantons)"
    fi
    
    echo "Executing spatial intersection query..."
    echo ""
    
    # Execute the spatial query
    $(get_timeout_cmd) duckdb -c "
    $(get_duckdb_prefix)
    
    -- First, verify the municipality exists
    SELECT 
        'Municipality found: ' || name || ' (BFS: ' || bfs_nummer || ')' AS info
    FROM 
        st_read('$SWISSBOUNDARIES_GPKG', layer='$SWISSBOUNDARIES_MUNICIPALITY_LAYER')
    WHERE 
        name = '$municipality_name'
    LIMIT 1;
    
    -- Main spatial intersection query
    SELECT 
        zones.$NPL_KOMMUNAL_ZONE_COLUMN AS zone_name,
        zones.$NPL_KANTONAL_ZONE_COLUMN AS kantonal_equivalent,
        COUNT(*) AS feature_count,
        zones.$NPL_CANTON_COLUMN AS canton
    FROM 
        '$NPL_GRUNDNUTZUNG_PARQUET' AS zones
    JOIN 
        st_read('$SWISSBOUNDARIES_GPKG', layer='$SWISSBOUNDARIES_MUNICIPALITY_LAYER') AS municipalities
    ON 
        ST_Intersects(zones.$NPL_GEOMETRY_COLUMN, municipalities.$SB_GEOMETRY_COLUMN)
    WHERE 
        municipalities.$SB_MUNICIPALITY_NAME_COLUMN = '$municipality_name'
        $canton_clause
    GROUP BY 
        zones.$NPL_KOMMUNAL_ZONE_COLUMN, zones.$NPL_KANTONAL_ZONE_COLUMN, zones.$NPL_CANTON_COLUMN
    ORDER BY 
        feature_count DESC
    LIMIT $MAX_RESULTS;
    "
}

# Main execution
if [ $# -eq 0 ]; then
    echo "Usage: $0 [municipality_name] [optional_canton]"
    echo ""
    echo "Examples:"
    echo "  $0 Steinhausen"
    echo "  $0 Steinhausen ZG"
    echo "  $0 Zurich ZH"
    echo "  $0 Basel BS"
    echo ""
    echo "To discover available municipalities, run:"
    echo "  ./scripts/discover_municipalities.sh"
    exit 1
fi

execute_spatial_query "$1" "$2"
