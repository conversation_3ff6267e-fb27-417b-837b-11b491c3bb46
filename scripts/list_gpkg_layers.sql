-- List all layers/tables in a GPKG file using DuckDB
INSTALL spatial;
LOAD spatial;

-- Method 1: Query the GPKG metadata tables directly
.print 'Method 1: GPKG Contents Table'
SELECT 
    table_name,
    data_type,
    identifier,
    description
FROM gpkg_contents 
ORDER BY table_name;

-- Method 2: Use SQLite system tables (GPKG is SQLite-based)
.print ''
.print 'Method 2: SQLite Master Table'
SELECT 
    name as table_name,
    type,
    sql
FROM sqlite_master 
WHERE type IN ('table', 'view')
    AND name NOT LIKE 'sqlite_%'
    AND name NOT LIKE 'gpkg_%'
    AND name NOT LIKE 'rtree_%'
ORDER BY name;
