#!/bin/bash

# Test script to verify spatial queries work with different municipalities

# Load configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/load_config.sh"

echo "=== Testing Spatial Queries ==="
echo ""

# Test function
test_municipality() {
    local municipality="$1"
    local canton="$2"
    
    echo "Testing: $municipality${canton:+ ($canton)}"
    echo "----------------------------------------"
    
    # Quick test to see if municipality exists
    local exists=$(duckdb -c "
    $(get_duckdb_prefix)
    SELECT COUNT(*) 
    FROM st_read('$SWISSBOUNDARIES_GPKG', layer='$SWISSBOUNDARIES_MUNICIPALITY_LAYER')
    WHERE name = '$municipality';
    " 2>/dev/null)
    
    if [ "$exists" = "1" ]; then
        echo "✓ Municipality found in SwissBoundaries3D"
        echo "Command: ./scripts/filter_zones_by_municipality.sh spatial $municipality${canton:+ $canton}"
    else
        echo "✗ Municipality not found in SwissBoundaries3D"
    fi
    echo ""
}

# Test known municipalities
echo "Testing known municipalities from configuration:"
echo ""

test_municipality "Steinhausen" "ZG"
test_municipality "Zurich" "ZH"
test_municipality "Basel" "BS"

echo "=== Manual Testing ==="
echo ""
echo "To test any municipality manually:"
echo "1. Find available municipalities: ./scripts/discover_municipalities.sh"
echo "2. Test spatial query: ./scripts/filter_zones_by_municipality.sh spatial [municipality_name]"
echo "3. Or use universal query: ./scripts/universal_spatial_query.sh [municipality_name]"
echo ""
