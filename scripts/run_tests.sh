#!/bin/bash

# Test Runner Script for Municipality Zones Viewer
# Provides different test execution options

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

show_help() {
    echo "Test Runner for Municipality Zones Viewer"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  all         Run all tests"
    echo "  unit        Run only unit tests"
    echo "  integration Run only integration tests"
    echo "  api         Run only API tests"
    echo "  services    Run only service tests"
    echo "  fast        Run fast tests (exclude slow tests)"
    echo "  coverage    Run tests with coverage report"
    echo "  watch       Run tests in watch mode"
    echo "  help        Show this help message"
    echo ""
    echo "Options:"
    echo "  -v, --verbose    Verbose output"
    echo "  -q, --quiet      Quiet output"
    echo "  -x, --exitfirst  Stop on first failure"
    echo "  --no-cov         Skip coverage collection"
    echo ""
    echo "Examples:"
    echo "  $0 all -v              # Run all tests with verbose output"
    echo "  $0 unit --exitfirst    # Run unit tests, stop on first failure"
    echo "  $0 fast                # Run only fast tests"
    echo "  $0 coverage            # Run tests with coverage report"
}

setup_environment() {
    cd "$PROJECT_DIR"
    
    # Activate virtual environment if it exists
    if [ -d ".venv" ]; then
        echo -e "${BLUE}Activating virtual environment...${NC}"
        source .venv/bin/activate
    else
        echo -e "${YELLOW}Warning: No virtual environment found${NC}"
    fi
    
    # Load environment variables
    if [ -f ".env" ]; then
        export $(grep -v '^#' .env | xargs)
    fi
}

run_tests() {
    local test_type="$1"
    shift
    local extra_args="$@"
    
    echo -e "${BLUE}Running $test_type tests...${NC}"
    
    case "$test_type" in
        "all")
            pytest tests/ $extra_args
            ;;
        "unit")
            pytest tests/ -m "unit" $extra_args
            ;;
        "integration")
            pytest tests/ -m "integration" $extra_args
            ;;
        "api")
            pytest tests/test_api/ $extra_args
            ;;
        "services")
            pytest tests/test_services/ $extra_args
            ;;
        "fast")
            pytest tests/ -m "not slow" $extra_args
            ;;
        "coverage")
            pytest tests/ --cov=app --cov-report=html --cov-report=term-missing $extra_args
            echo -e "${GREEN}Coverage report generated in htmlcov/index.html${NC}"
            ;;
        "watch")
            echo -e "${YELLOW}Running tests in watch mode (Ctrl+C to stop)${NC}"
            pytest-watch tests/ $extra_args
            ;;
        *)
            echo -e "${RED}Unknown test type: $test_type${NC}"
            show_help
            exit 1
            ;;
    esac
}

check_dependencies() {
    echo -e "${BLUE}Checking test dependencies...${NC}"
    
    # Check if pytest is installed
    if ! command -v pytest &> /dev/null; then
        echo -e "${RED}pytest not found. Installing test dependencies...${NC}"
        pip install -e .[test]
    fi
    
    # Check if coverage is needed and available
    if [[ "$1" == "coverage" ]] && ! command -v pytest-cov &> /dev/null; then
        echo -e "${YELLOW}Installing coverage dependencies...${NC}"
        pip install pytest-cov
    fi
    
    # Check if watch mode is needed
    if [[ "$1" == "watch" ]] && ! command -v pytest-watch &> /dev/null; then
        echo -e "${YELLOW}Installing watch dependencies...${NC}"
        pip install pytest-watch
    fi
}

cleanup() {
    echo -e "${BLUE}Cleaning up test artifacts...${NC}"
    find tests/ -name "*.pyc" -delete 2>/dev/null || true
    find tests/ -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    rm -rf .pytest_cache 2>/dev/null || true
}

main() {
    local command="${1:-all}"
    shift || true
    
    case "$command" in
        "help"|"-h"|"--help")
            show_help
            exit 0
            ;;
        "clean")
            cleanup
            exit 0
            ;;
    esac
    
    setup_environment
    check_dependencies "$command"
    
    # Parse additional arguments
    local pytest_args=""
    while [[ $# -gt 0 ]]; do
        case $1 in
            -v|--verbose)
                pytest_args="$pytest_args -v"
                shift
                ;;
            -q|--quiet)
                pytest_args="$pytest_args -q"
                shift
                ;;
            -x|--exitfirst)
                pytest_args="$pytest_args -x"
                shift
                ;;
            --no-cov)
                # Skip coverage for this run
                shift
                ;;
            *)
                pytest_args="$pytest_args $1"
                shift
                ;;
        esac
    done
    
    # Run the tests
    if run_tests "$command" $pytest_args; then
        echo -e "${GREEN}✅ Tests completed successfully!${NC}"
        exit 0
    else
        echo -e "${RED}❌ Tests failed!${NC}"
        exit 1
    fi
}

# Run main function with all arguments
main "$@"
