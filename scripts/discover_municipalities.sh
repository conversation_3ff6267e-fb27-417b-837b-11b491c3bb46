#!/bin/bash

# Script to discover all available municipalities in SwissBoundaries3D
# This helps populate the configuration with actual municipality data

# Load configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/load_config.sh"

echo "=== Discovering Municipalities in SwissBoundaries3D ==="
echo ""

echo "Querying all municipalities with their BFS numbers and cantons..."
echo ""

# Query all municipalities with their details
duckdb -c "
$(get_duckdb_prefix)
SELECT 
    municipalities.name AS municipality_name,
    municipalities.bfs_nummer AS bfs_number,
    cantons.name AS canton_name,
    municipalities.kantonsnummer AS canton_number
FROM 
    st_read('$SWISSBOUNDARIES_GPKG', layer='$SWISSBOUNDARIES_MUNICIPALITY_LAYER') AS municipalities
LEFT JOIN
    st_read('$SWISSBOUNDARIES_GPKG', layer='$SWISSBOUNDARIES_CANTON_LAYER') AS cantons
ON 
    municipalities.kantonsnummer = cantons.kantonsnummer
ORDER BY 
    canton_name, municipality_name
LIMIT 50;
" 2>/dev/null

echo ""
echo "=== Sample Usage ==="
echo "Use any of these municipalities with the spatial filter:"
echo ""
echo "Examples:"
echo "./scripts/filter_zones_by_municipality.sh spatial Steinhausen"
echo "./scripts/filter_zones_by_municipality.sh spatial Zurich ZH"
echo "./scripts/filter_zones_by_municipality.sh spatial Basel BS"
echo ""
