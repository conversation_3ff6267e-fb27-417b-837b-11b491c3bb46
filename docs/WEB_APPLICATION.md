# Web Application User Guide

## Overview

The Municipality Zones Viewer is a FastAPI web application that provides interactive visualization of Swiss spatial planning data (Nutzungsplan). It combines OpenLayers mapping with HTMX for dynamic content updates.

## Getting Started

### Starting the Application

```bash
# Start the web server
./start_server.sh

# Access the application
# Open http://localhost:8000 in your browser
```

The application will automatically:
- Activate the Python virtual environment
- Start the FastAPI server with hot reload
- Load spatial data from processed Parquet files

## User Interface

### Layout

The application uses a three-section layout:

1. **Map Section (Top)**: Interactive map with zone visualization
2. **Query Section (Middle)**: Municipality selection and controls
3. **Results Section (Bottom)**: Data display with multiple tabs

### Map Controls

- **🔍 Zoom to Municipality**: Centers map on selected municipality
- **🗺️ Toggle Swiss/OSM Map**: Switches between Swiss official maps and OpenStreetMap

## Data Views

### Table View Tab ✅ **Fully Functional**

Displays zone data in a structured table format:

- **Zone Name**: Local municipality zone designation
- **Type**: Zone category (grundnutzung, ueberlagernde, etc.)
- **Description**: Additional zone information
- **Area**: Zone area in square meters
- **Canton**: Swiss canton abbreviation
- **Actions**: Detail buttons for individual zones

### Summary Tab ⚠️ **Partially Implemented**

Shows aggregated statistics about loaded zones:

- Total number of zones found
- Municipality name (if filtered)
- Zone type (if filtered)
- Total area of all zones combined

**Current Status**: Basic summary info is available in the table template but not fully populated in the Summary tab.

### GeoJSON Tab ⚠️ **Not Implemented**

Intended to display raw GeoJSON data for zones:

- Structured GeoJSON format used by the map
- Useful for developers or data export
- **Current Status**: Shows placeholder text only

## Zone Limits and Pagination

### API Limits

- **Default Limit**: 100 zones per request
- **Maximum Limit**: 1,000 zones per request
- **Configuration**: Set in `config/app_settings.py`

### Requesting More Zones

You can increase the zone limit by modifying the API request:

```bash
# Default: Returns up to 100 zones
GET /api/zones/?municipality=Zurich

# Custom limit: Returns up to 500 zones
GET /api/zones/?municipality=Zurich&limit=500

# Maximum: Returns up to 1,000 zones
GET /api/zones/?municipality=Zurich&limit=1000
```

### GeoJSON Data

The map's GeoJSON endpoint (`/municipality/{id}/geojson`) has **no explicit limit** and returns all zones for the selected municipality, which may be significantly more than the table view limit.

## Features

### Municipality Selection

1. **Dropdown Menu**: Select from all available Swiss municipalities
2. **Auto-complete**: Type to search for specific municipalities
3. **Municipality Count**: Shows total available municipalities

### Zone Loading

1. **Select Municipality**: Choose from dropdown
2. **Click "Load Zones"**: Fetches zone data via HTMX
3. **View Results**: Data appears in selected tab

### Map Interaction

- **Click Zones**: Shows popup with zone information
- **Zoom Controls**: Standard map navigation
- **Layer Toggle**: Switch between background map sources

## Technical Details

### Architecture

- **Backend**: FastAPI with DuckDB spatial queries
- **Frontend**: OpenLayers + HTMX (minimal JavaScript)
- **Data**: Processed Parquet files from Swiss spatial data
- **Styling**: Custom CSS with responsive design

### API Endpoints

- `GET /`: Main application page
- `GET /api/municipalities/selector`: Municipality dropdown
- `GET /api/zones/`: Zone data (with limits)
- `GET /api/zones/municipality/{id}/geojson`: Map data (no limits)
- `GET /api/zones/{id}`: Individual zone details

### Data Sources

- **Geodienste Data**: Federal spatial planning data (1,563 municipalities)
- **SwissBoundaries3D**: Municipality boundaries (all 2,140+ municipalities)
- **ZH Data**: Canton Zurich specific data (if available)

## Troubleshooting

### No Zones Found

1. **Check Data Preparation**: Ensure `./raw_input_data/prepare_data.sh` has been run
2. **Verify Municipality**: Confirm municipality exists in SwissBoundaries3D
3. **Check Logs**: Look for spatial query errors in server logs

### Map Not Loading

1. **Internet Connection**: OpenLayers requires internet for tile loading
2. **JavaScript Errors**: Check browser console for errors
3. **Port Conflicts**: Ensure port 8000 is available

### Performance Issues

1. **Large Municipalities**: Some municipalities have thousands of zones
2. **Increase Limits**: Use higher limit values for complete data
3. **Browser Memory**: Large datasets may impact browser performance

## Development

### Adding New Features

1. **Backend**: Add endpoints in `app/api/`
2. **Frontend**: Update templates in `app/templates/`
3. **Styling**: Modify `app/static/css/styles.css`
4. **JavaScript**: Update `app/static/js/map.js`

### Configuration

- **App Settings**: `config/app_settings.py`
- **Database Config**: `app/core/config.py`
- **Data Sources**: `config/data_sources.conf`

## Future Enhancements

### Planned Features

- **Complete Summary Tab**: Full statistical analysis
- **Implement GeoJSON Tab**: Raw data display and export
- **Pagination**: Handle large datasets more efficiently
- **Search Functionality**: Find zones by name or type
- **Export Options**: Download data in various formats

### Known Limitations

- Summary and GeoJSON tabs need implementation
- No pagination for large zone datasets
- Limited to Swiss spatial data only
- Requires internet connection for map tiles
