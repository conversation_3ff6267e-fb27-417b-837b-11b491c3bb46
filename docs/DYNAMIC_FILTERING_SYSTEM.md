# Dynamic Filtering System

## Overview

The Dynamic Filtering System automatically generates filtering options based on the actual data schema and content of loaded GPKG files or DuckDB databases. This eliminates hardcoded filter definitions and creates a truly adaptive system that works with any Swiss municipality zone dataset structure.

## Architecture

### Core Components

1. **Schema Detection Service** (`app/services/schema_detection_service.py`)
   - Inspects database tables to extract column metadata
   - Analyzes data types, unique values, and value ranges
   - Caches schema information for performance

2. **Filter Generation Service** (`app/services/filter_generation_service.py`)
   - Creates appropriate filter UI components based on column characteristics
   - Maps German display names for common Swiss geodata columns
   - Generates filter configurations dynamically

3. **Dynamic Filter Models** (`app/models/filter.py`)
   - Pydantic models for filter definitions and metadata
   - Support for categorical, numeric, text, date, and boolean filters
   - Type-safe filter request/response handling

4. **Filter API Endpoints** (`app/api/filters.py`)
   - RESTful endpoints for filter schema and data retrieval
   - HTMX integration for dynamic UI updates
   - Caching support for performance optimization

### Filter Types

The system automatically determines the appropriate filter type based on data characteristics:

#### Categorical Filters
- **Trigger**: String columns with 2-50 unique values
- **UI**: Dropdown selector with value counts
- **Example**: Canton (ZH, BE, VD), Zone types

#### Numeric Range Filters  
- **Trigger**: Integer/float columns or numeric columns with >50 unique values
- **UI**: Min/max input fields with range validation
- **Example**: Area measurements, building heights

#### Text Search Filters
- **Trigger**: String columns with >50 unique values and average length >3
- **UI**: Text input with LIKE search
- **Example**: Zone descriptions, addresses

#### Boolean Filters
- **Trigger**: Boolean columns
- **UI**: Yes/No dropdown
- **Example**: Active status, public access

#### Date Range Filters
- **Trigger**: Date/datetime columns
- **UI**: Date picker inputs for start/end dates
- **Example**: Publication dates, validity periods

## Configuration

### German Labels
The system includes German translations for common Swiss geodata columns:

```python
"german_labels": {
    "typ_kommunal_bezeichnung": "Kommunale Zonenbezeichnung",
    "typ_kantonal_bezeichnung": "Kantonale Zonenbezeichnung", 
    "typ_kommunal_code": "Kommunaler Code",
    "kanton": "Kanton",
    "area": "Fläche",
    # ... more mappings
}
```

### Filter Generation Rules
- **Categorical threshold**: Max 50 unique values for dropdown filters
- **Min unique for categorical**: At least 2 unique values required
- **Text search min length**: Average length ≥3 characters
- **Excluded columns**: `["id", "geom", "geometry", "bbox"]`

## API Endpoints

### Get Filters for Data Source
```
GET /api/filters/schema/{data_source_id}
```
Returns available filters for a specific data source.

**Response (JSON)**:
```json
{
  "data_source_id": "820737",
  "data_source_name": "NPL Grundnutzung",
  "table_name": "npl_grundnutzung",
  "filters": [
    {
      "id": "npl_grundnutzung_kanton",
      "column_name": "kanton",
      "display_name": "Kanton",
      "filter_type": "categorical",
      "data_type": "string",
      "options": [
        {"value": "ZH", "label": "ZH", "count": 1000},
        {"value": "BE", "label": "BE", "count": 800}
      ]
    }
  ]
}
```

**Response (HTMX)****: Returns HTML filter selector component.

### Get Column Values
```
GET /api/filters/values/{data_source_id}/{column_name}?limit=100
```
Returns unique values for a specific column with counts.

### Get Filter Selector HTML
```
GET /api/filters/selector/{data_source_id}
```
Returns HTMX-compatible HTML for filter UI components.

## Frontend Integration

### HTMX Integration
The system integrates seamlessly with the existing HTMX-based interface:

1. **Data Source Selection**: Triggers filter loading via `loadDynamicFilters()`
2. **Filter Application**: Auto-applies filters with debouncing (500ms delay)
3. **Dynamic Updates**: Filters update when data source selection changes

### JavaScript Functions
```javascript
// Load filters for selected data sources
function loadDynamicFilters() {
    const checkedSources = document.querySelectorAll('input[name="data_source"]:checked');
    // ... implementation
}

// Clear all filter inputs
function clearFilters(dataSourceId) {
    // ... implementation
}
```

### CSS Styling
Filter components use responsive grid layout with consistent styling:
- Grid layout adapts to available space
- Form controls follow application design system
- Loading states and error handling included

## Database Integration

### Dynamic Query Building
The zone service builds dynamic WHERE clauses based on filter parameters:

```python
def _build_dynamic_filter_conditions(self, dynamic_filters: Dict[str, Any]) -> tuple[str, List[Any]]:
    # Handles different filter types:
    # - Categorical: IN clauses
    # - Range: >= and <= conditions  
    # - Text: LIKE with wildcards
    # - Exact match: = conditions
```

### Query Parameter Handling
Filter parameters are extracted from HTTP query strings:
```python
# Extract dynamic filter parameters from query string
dynamic_filters = {}
for key, value in request.query_params.items():
    if key.startswith('filter_') and value:
        filter_id = key[7:]  # Remove 'filter_' prefix
        dynamic_filters[filter_id] = value
```

## Performance Optimization

### Caching Strategy
1. **Schema Cache**: Table metadata cached in memory
2. **Filter Cache**: Generated filter sets cached by data source
3. **Value Cache**: Unique column values cached with configurable TTL

### Database Optimization
- Spatial indexes on geometry columns
- Efficient unique value queries with LIMIT
- Parameterized queries prevent SQL injection

### UI Optimization
- Debounced filter application (500ms)
- Progressive loading of filter options
- Minimal DOM updates via HTMX

## Usage Examples

### Basic Filter Application
1. User selects a data source (e.g., "NPL Grundnutzung")
2. System automatically loads available filters
3. User selects filter values (e.g., Canton = "ZH")
4. Results update automatically via HTMX

### Advanced Filtering
```html
<!-- Multiple filters applied simultaneously -->
<select name="filter_npl_grundnutzung_kanton">
    <option value="ZH">Zürich (1000)</option>
</select>

<input type="text" name="filter_npl_grundnutzung_typ_kommunal_bezeichnung" 
       placeholder="Nach Zonenbezeichnung suchen...">

<input type="number" name="filter_npl_grundnutzung_area_min" 
       placeholder="Min: 0">
```

## Error Handling

### Graceful Degradation
- Falls back to basic filtering if schema detection fails
- Shows placeholder message when no filters available
- Handles missing or invalid data gracefully

### Logging
- Comprehensive logging for debugging
- Performance metrics for optimization
- Error tracking for reliability

## Future Enhancements

### Planned Features
1. **Multi-table Joins**: Filters across related tables
2. **Spatial Filters**: Geographic boundary filtering
3. **Advanced Operators**: NOT, OR, complex conditions
4. **Filter Presets**: Save/load common filter combinations
5. **Export Options**: Filtered data export functionality

### Performance Improvements
1. **Background Caching**: Pre-generate filter metadata
2. **Incremental Updates**: Update only changed filters
3. **Lazy Loading**: Load filter options on demand
4. **Database Indexes**: Optimize for common filter columns
