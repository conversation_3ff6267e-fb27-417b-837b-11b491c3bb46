# SwissBoundaries3D GPKG Layers Information

## Available Layers in SwissBoundaries3D

Using `ogrinfo` to list all layers:

```
1: tlm_hoheitsgrenze (3D Line String)     - Administrative boundaries (lines)
2: tlm_hoheitsgebiet (3D Multi Polygon)   - Administrative territories (municipalities)
3: tlm_bezirksgebiet (3D Multi Polygon)   - District territories
4: tlm_landesgebiet (3D Multi Polygon)    - Country territory
5: tlm_kantonsgebiet (3D Multi Polygon)   - Canton territories
```

## Municipality Layer Details (`tlm_hoheitsgebiet`)

- **Geometry Type**: 3D Multi Polygon
- **Feature Count**: 2,141 municipalities
- **Coordinate System**: CH1903+ / LV95 (EPSG:2056)

### Key Columns:
- `name`: Municipality name (e.g., "Steinhausen")
- `bfs_nummer`: BFS number (e.g., 1708 for Steinhausen)
- `kantonsnummer`: Canton number
- `bezirksnummer`: District number
- `einwohnerzahl`: Population
- `gem_flaeche`: Municipality area
- `geom`: Geometry (polygon boundaries)

## Steinhausen Municipality

- **Name**: Steinhausen
- **BFS Number**: 1708
- **Found in layer**: `tlm_hoheitsgebiet`

## DuckDB Commands to List Layers

```sql
-- Method 1: Using ogrinfo (most reliable)
ogrinfo -so raw_input_data/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg

-- Method 2: Using DuckDB spatial extension
INSTALL spatial;
LOAD spatial;
SELECT table_name, data_type FROM gpkg_contents ORDER BY table_name;

-- Method 3: Using sqlite3 (GPKG is SQLite-based)
sqlite3 raw_input_data/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg "SELECT table_name FROM gpkg_contents;"
```

## Finding Steinhausen

```bash
# Using ogrinfo to find Steinhausen
ogrinfo -sql "SELECT name, bfs_nummer FROM tlm_hoheitsgebiet WHERE name LIKE '%Steinhausen%'" raw_input_data/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg
```

## Next Steps for Spatial Queries

Now that we know:
- **Layer name**: `tlm_hoheitsgebiet`
- **Municipality column**: `name`
- **Steinhausen exists** with BFS number 1708

We can create spatial intersection queries to find zones within Steinhausen municipality boundaries.
