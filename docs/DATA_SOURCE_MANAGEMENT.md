# Data Source Management

## <PERSON><PERSON>blick

Das Swiss Geodata Accessibility Platform verwendet ein modulares System zur Verwaltung verschiedener Schweizer Geodatenquellen. Das System integriert die offizielle [Schweizer Geodateninfrastruktur API](https://api.geobasisdaten.ch/api/v1/data/) und ermöglicht es, lokale Datensätze mit den offiziellen Metadaten zu verknüpfen.

## Architektur

### Datenquellen-Typen

1. **Verfügbare Datenquellen** (Schwarz angezeigt)
   - Lokal verfügbare und verarbeitete Datensätze
   - Vollständig funktionsfähig in der Anwendung
   - Beispiele: Nutzungsplanung, SwissBoundaries3D

2. **Geplante Datenquellen** (Grau angezeigt)
   - In der offiziellen API verfügbar, aber noch nicht lokal implementiert
   - Zeigt das Potenzial für zukünftige Erweiterungen
   - Über 190 verschiedene Geodatensätze verfügbar

### Komponenten

#### DataSourceService
- Zentrale Verwaltung aller Datenquellen
- Abruf und Caching der offiziellen API-Daten
- Verknüpfung lokaler Datensätze mit API-Metadaten

#### DataSource Models
- `LegislativeEntry`: Offizielle Geodateneinträge aus der API
- `LocalDataSource`: Lokale Datensatz-Konfiguration
- `DataSourceInfo`: Kombinierte Informationen aus beiden Quellen

#### UI-Komponenten
- Datenquellen-Selektor mit Verfügbarkeitsstatus
- Informations-Buttons (ℹ️) für detaillierte Metadaten
- Modulare Workflow-Schritte

## Verwendung

### 1. Datenquelle auswählen
- Verfügbare Quellen sind schwarz und auswählbar
- Geplante Quellen sind grau und deaktiviert
- Informations-Button zeigt Details zu jeder Quelle

### 2. Gemeinde auswählen
- Wird nach Datenquellen-Auswahl aktiviert
- Funktioniert mit allen verfügbaren Datenquellen

### 3. Daten filtern und anzeigen
- Abhängig von der gewählten Datenquelle
- Verschiedene Filter und Ansichten verfügbar

## Neue Datenquellen hinzufügen

### Schritt 1: Daten vorbereiten
```bash
# Neue Daten in raw_input_data/ ablegen
# Konvertierung zu Parquet-Format
# Verarbeitung für DuckDB-Kompatibilität
```

### Schritt 2: LocalDataSource konfigurieren
```python
# In app/services/data_source_service.py
"neue_quelle": LocalDataSource(
    id="neue_quelle",
    name="XX.Y Datenquellenname",
    description="Beschreibung der Datenquelle",
    status=DataSourceStatus.AVAILABLE,
    file_paths=["pfad/zu/daten.parquet"],
    metadata={"labelNumber": XX}  # Für API-Verknüpfung
)
```

### Schritt 3: Services erweitern
- Neue Abfrage-Logik in entsprechenden Services
- API-Endpunkte für neue Datentypen
- UI-Anpassungen für spezifische Datenstrukturen

## API-Integration

### Caching
- API-Daten werden lokal gecacht (`data/cache/legislative_entries.json`)
- Reduziert API-Aufrufe und verbessert Performance
- Cache wird bei Bedarf automatisch aktualisiert

### Metadaten-Verknüpfung
- Lokale Datensätze werden über `labelNumber` mit API-Einträgen verknüpft
- Automatische Aktualisierung der `legislative_entry_id`
- Vollständige Metadaten-Integration

## Vorteile

1. **Skalierbarkeit**: Einfache Integration neuer Datenquellen
2. **Transparenz**: Vollständige Übersicht über verfügbare Geodaten
3. **Standardkonformität**: Integration mit offizieller Schweizer Geodateninfrastruktur
4. **Benutzerfreundlichkeit**: Klare Unterscheidung zwischen verfügbaren und geplanten Quellen
5. **Metadaten-Reichtum**: Vollständige Dokumentation und Links zu offiziellen Quellen

## Zukünftige Entwicklung

Das System ist darauf ausgelegt, schrittweise weitere der 190+ verfügbaren Schweizer Geodatenquellen zu integrieren, basierend auf Benutzerbedürfnissen und Prioritäten.
