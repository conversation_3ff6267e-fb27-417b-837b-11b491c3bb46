# Data Availability Logic

This document explains how the system determines if a municipality has data available for a specific data source.

## Overview

The Universal Municipality Filter checks data availability for each municipality when a data source is selected. This prevents users from selecting municipalities that have no data, improving the user experience.

## Data Source Mapping

```python
data_source_mapping = {
    "820737": "geodienste_grundnutzung",      # Geodienste Nutzungsplanung
    "821838": "swiss_municipalities",  # SwissBoundaries3D
    "nutzungsplanung": "geodienste_grundnutzung",     # Legacy support
    "swissboundaries": "swiss_municipalities"   # Legacy support
}
```

## Availability Logic by Data Source

### SwissBoundaries3D (Technical ID: 821838)
- **Logic**: All municipalities are available
- **Reason**: This is boundary data covering all Swiss municipalities
- **Implementation**: Always returns `True`

### Geodienste Nutzungsplanung (Technical ID: 820737)
- **Logic**: Real-time spatial intersection queries with DuckDB
- **Reason**: Geodienste data is not available for all municipalities
- **Implementation**: Spatial intersection between Geodienste zones and municipality boundaries

#### Municipalities with Geodienste Data
The system uses real-time spatial queries to determine which municipalities have Geodienste data:

**Major Cities:**
- Zürich, Basel, Genf, Bern, Lausanne, Winterthur
- Luzern, St. Gallen, Lugano, Biel/Bienne, Thun
- Köniz, La Chaux-de-Fonds, Schaffhausen, Fribourg
- Chur, Vernier, Neuchâtel, Uster, Sion, Zug

**Zurich Region:**
- Aarau, Baden, Wettingen, Dietikon, Schlieren
- Dübendorf, Wädenswil, Horgen, Thalwil, Adliswil
- Kloten, Bülach, Opfikon, Wallisellen, Regensdorf

**Central Switzerland:**
- Steinhausen, Baar, Cham, Hünenberg, Risch

**Canton Capitals:**
- Liestal, Solothurn, Frauenfeld, Herisau, Appenzell
- Glarus, Altdorf, Schwyz, Sarnen, Stans
- Bellinzona, Delémont

**Additional Regional Centers:**
- Rapperswil-Jona, Wil, Gossau, Uzwil, Flawil
- Kreuzlingen, Arbon, Romanshorn, Amriswil
- Olten, Grenchen, Langenthal, Burgdorf
- Muttenz, Allschwil, Reinach, Pratteln
- Nyon, Morges, Renens, Ecublens, Pully
- Monthey, Sierre, Martigny, Brig-Glis
- Locarno, Mendrisio, Chiasso

## Performance Considerations

### Why Not Real-Time Spatial Queries?

Initial implementation attempted real-time spatial intersection queries:
```sql
SELECT COUNT(*)
FROM npl_grundnutzung zones
JOIN swiss_municipalities muni
    ON ST_Intersects(zones.geom, muni.geometry)
WHERE muni.tlm_hoheitsgebiet_name = ?
```

**Problems:**
- Queries took 10+ seconds per municipality
- UI became unresponsive
- Poor user experience

### Current Approach: Pre-Computed Lists

**Benefits:**
- Instant response time
- Smooth UI interactions
- Predictable performance
- Easy to maintain and update

**Trade-offs:**
- Requires manual curation of municipality lists
- May not reflect real-time data changes
- Needs updates when new data becomes available

## Adding New Data Sources

To add availability logic for a new data source:

1. **Add to mapping:**
```python
data_source_mapping["new_tech_id"] = "new_table_name"
```

2. **Add availability logic:**
```python
elif data_table == "new_table_name":
    # Your availability logic here
    return check_availability_for_new_source(municipality_name)
```

3. **Choose approach:**
   - **All available**: Return `True` (like SwissBoundaries3D)
   - **Subset available**: Create curated list (like NPL)
   - **Dynamic checking**: Use database queries (if performance allows)

## Future Improvements

### Potential Enhancements:
1. **Background pre-computation**: Calculate availability offline and cache results
2. **Database-driven lists**: Store availability in database tables
3. **API-based checking**: Query data source APIs for coverage information
4. **User feedback**: Allow users to report missing/incorrect availability

### Monitoring:
- Track which municipalities users try to select but have no data
- Monitor performance of availability checks
- Update municipality lists based on new data releases
