# Spatial Filtering Fix: Addressing Extra Zones Display Issue

## Problem Identified

The application was displaying more zones than actually belong to a municipality because it was using `ST_Intersects()` for spatial filtering, which includes any zone that touches or overlaps the municipality boundary, even if only a tiny portion is within the municipality.

### Test Results (Steinhausen Municipality)
- **Before Fix (ST_Intersects only)**: 225 zones
- **After Fix (80% threshold)**: 184 zones
- **Strict (ST_Within only)**: 154 zones

**Result**: The fix eliminates 41 extra zones (225 - 184 = 41) that were only marginally within the municipality.

## Root Cause

### ST_Intersects() Behavior
`ST_Intersects(zone_geometry, municipality_geometry)` returns `true` if:
- The zone is completely within the municipality
- The zone partially overlaps the municipality
- The zone only touches the municipality boundary

This means zones that span multiple municipalities (like roads, railways, forests, agricultural areas) were being included even when only a small fraction was within the selected municipality.

### Types of Zones Affected
The "extra" zones were primarily:
- **Verkehrsflaeche** (Traffic areas/roads)
- **Wald** (Forest areas)
- **Landwirtschaftszone** (Agricultural zones)
- **Bahnareal** (Railway areas)
- **<PERSON>urschutz kantonal** (Nature protection areas)

These zones naturally span across municipality boundaries.

## Solution Implemented

### Threshold-Based Filtering
Instead of simple intersection, the application now uses area-based filtering:

```sql
-- Old approach (problematic)
ST_Intersects(zones.geom, municipalities.geometry)

-- New approach (fixed)
ST_Intersects(zones.geom, municipalities.geometry)
AND (ST_Area(ST_Intersection(zones.geom, municipalities.geometry)) / ST_Area(zones.geom)) >= 0.8
```

### Configuration
The threshold is configurable in `app/core/config.py`:

```python
# Spatial filtering settings
# Minimum percentage of zone area that must be within municipality to include it
# 0.8 = 80% of zone must be within municipality boundary
# 1.0 = zone must be completely within municipality (ST_Within behavior)
# 0.0 = any intersection includes the zone (ST_Intersects behavior)
spatial_filter_threshold: float = 0.8
```

### Alternative Approaches Considered

1. **Strict Filtering (ST_Within)**: Only zones completely inside municipality
   - Pros: No boundary-crossing zones
   - Cons: May exclude legitimate zones that slightly cross boundaries

2. **Geometry Clipping**: Show only parts of zones within municipality
   - Pros: Most accurate representation
   - Cons: Complex implementation, may fragment zones visually

3. **Threshold Filtering (Implemented)**: Zones with X% area within municipality
   - Pros: Balanced approach, configurable, eliminates most false positives
   - Cons: Still some subjectivity in threshold choice

## Files Modified

1. **`app/services/zone_service.py`**:
   - Updated `get_zones()` method for grundnutzung and ueberlagernde_flaechen
   - Updated `get_municipality_zones_geojson()` method
   - Added percentage_within property to GeoJSON features

2. **`app/core/config.py`**:
   - Added `spatial_filter_threshold` configuration option

## Impact

### Before Fix
- Showed zones that barely touched municipality boundaries
- Included cross-boundary infrastructure zones
- Potentially confusing for users expecting only "local" zones

### After Fix
- Shows only zones where 80%+ of the area is within the municipality
- Eliminates most cross-boundary false positives
- Maintains reasonable coverage of legitimate zones
- Configurable threshold allows fine-tuning

## Testing

The fix can be verified by comparing zone counts before and after implementation:

```sql
-- Before fix (ST_Intersects only): 225 zones
-- After fix (80% threshold): 184 zones
-- Difference: 41 zones eliminated
```

## Future Considerations

1. **User Interface**: Consider adding a toggle for different filtering modes
2. **Performance**: Monitor query performance with the additional area calculations
3. **Threshold Tuning**: May need adjustment based on user feedback
4. **Documentation**: Update user documentation to explain filtering behavior

## Technical Notes

- The fix uses `ST_Area(ST_Intersection())` to calculate overlap area
- Performance impact is minimal as it only adds one additional calculation
- The threshold is applied consistently across all zone types
- GeoJSON output now includes `percentage_within` property for transparency
