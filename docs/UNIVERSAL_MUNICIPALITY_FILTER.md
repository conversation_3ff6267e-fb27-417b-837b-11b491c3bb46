# Universal Municipality Filter

This system provides **universal spatial filtering** for any Swiss municipality across all data sources with intelligent availability detection.

## 🎯 **Key Features**

- ✅ **Works with ALL Swiss municipalities** (2,140+ municipalities from SwissBoundaries3D)
- ✅ **Data source aware** - shows availability per selected data source
- ✅ **Smart UI indicators** - municipalities greyed out when no data available
- ✅ **Technical ID integration** - uses official Swiss geodata infrastructure IDs
- ✅ **Real-time filtering** - municipalities reload when data source changes
- ✅ **Performance optimized** - fast availability checking without expensive spatial queries

## 📁 **File Structure**

```
config/
├── data_sources.conf           # All configuration variables and paths

scripts/
├── load_config.sh             # Configuration loader with helper functions
├── filter_zones_by_municipality.sh  # Main enhanced filter script
├── universal_spatial_query.sh # Universal spatial query for any municipality
├── discover_municipalities.sh # Discover available municipalities
├── test_spatial_queries.sh    # Test script
└── steinhausen_zones_spatial.sql    # Example SQL query
```

## 🚀 **Usage Examples**

### **1. Web Application (Primary Interface)**
```bash
# Start the web application
./start_server.sh
# Access at http://localhost:8000

# Then in the web interface:
# 1. Select a data source (e.g., "73.1 Nutzungsplanung")
# 2. Municipalities automatically reload with availability indicators
# 3. Available municipalities: normal text, selectable
# 4. Unavailable municipalities: greyed out, marked "(no data)"
```

### **2. API Usage**
```bash
# Get municipalities with availability for specific data source
curl "http://localhost:8000/api/municipalities/?data_source=820737"

# Get municipality selector HTML with availability indicators
curl "http://localhost:8000/api/municipalities/selector?data_source=821838"
```

### **3. Command Line (Legacy)**
```bash
./scripts/filter_zones_by_municipality.sh spatial Steinhausen
./scripts/filter_zones_by_municipality.sh spatial Zurich
./scripts/filter_zones_by_municipality.sh spatial Basel
```

## ⚙️ **Configuration**

All settings are in `config/data_sources.conf`:

- **File paths** - Geodienste data, SwissBoundaries3D, output locations
- **Column names** - Database column mappings
- **Canton codes** - All Swiss canton abbreviations
- **Known municipalities** - Pre-configured municipality list with BFS codes
- **Query settings** - Timeouts, limits, DuckDB settings

## 🔧 **How It Works**

### **Data Source Selection**
1. **User selects data source**: From available or planned sources (345 total)
2. **HTMX triggers reload**: Municipality selector reloads with `data_source` parameter
3. **Availability checking**: Each municipality checked for data availability in selected source

### **Availability Determination**
- **SwissBoundaries3D (821838)**: All municipalities available (boundary data)
- **Geodienste Nutzungsplanung (820737)**: Real-time spatial intersection (1,563 municipalities)
- **Future data sources**: Extensible system for any data source

### **Performance Optimization**
- **Efficient spatial queries**: DuckDB handles `ST_Intersects()` efficiently
- **Real-time accuracy**: Based on actual spatial data coverage
- **Fast UI response**: Municipalities load with accurate availability indicators

## 📊 **Example Output**

For Steinhausen:
```
=== Zones in municipality: Steinhausen (using spatial intersection) ===
Auto-detected canton: ZG

┌─────────────────────────────────┬──────────────────────────────┬───────────────┬────────┐
│           zone_name             │     kantonal_equivalent      │ feature_count │ canton │
├─────────────────────────────────┼──────────────────────────────┼───────────────┼────────┤
│ Verkehrsflaeche                 │ Verkehrsflaeche              │            64 │ ZG     │
│ Wald                           │ Wald                         │            26 │ ZG     │
│ Landwirtschaftszone            │ Landwirtschaftszone          │            19 │ ZG     │
│ Arbeitszone A                  │ Arbeitszone A                │            19 │ ZG     │
│ Wohnzone 1                     │ Wohnzone 1                   │            16 │ ZG     │
└─────────────────────────────────┴──────────────────────────────┴───────────────┴────────┘
```

## 🌍 **Supported Municipalities**

The system works with **ALL 2,141 Swiss municipalities** in SwissBoundaries3D, including:

- **Zug Canton**: Steinhausen, Zug, Baar, Cham, Hünenberg, etc.
- **Zurich Canton**: Zurich, Winterthur, etc.
- **Major Cities**: Basel, Bern, Geneva, Lausanne, Lucerne, St. Gallen
- **Any municipality**: Just use the exact name from SwissBoundaries3D

## 🔍 **Finding Municipality Names**

Use the discovery script to find exact municipality names:
```bash
./scripts/discover_municipalities.sh | grep -i "your_search_term"
```

## ⚡ **Performance**

- **Spatial queries**: ~30-60 seconds depending on municipality size
- **Configuration-driven**: Easy to maintain and modify
- **Timeout protection**: Queries timeout after 60 seconds by default
- **Result limits**: Limited to 100 results by default (configurable)
