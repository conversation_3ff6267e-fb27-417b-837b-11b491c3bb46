AI Development Guide: Nutzungszonen POC Web Application

Project Goal: Create a Proof-of-Concept (POC) web application that allows users to select a municipality and view its "Nutzungszonen" (land use zones). Data should be displayed on an interactive map and in an accompanying table. The system must be easily accessible and leverage DuckDB for data querying.

Recommended Tech Stack:

    Backend Framework: FastAPI (Python)

        Role: Serves as the API layer. Handles HTTP requests, interacts with DuckDB, processes data, and sends responses back to the frontend. Will also serve the main HTML shell.

    Database: DuckDB (with Spatial Extension)

        Role: Stores and queries the geospatial data (municipalities, Nutzungszonen) and their attributes.

    Frontend Interaction Logic: HTMX

        Role: Manages dynamic updates to parts of the HTML page (like the municipality list or attribute table) by making requests to FastAPI and swapping HTML fragments. Reduces the need for extensive custom JavaScript for many UI interactions.

    Mapping Library: OpenLayers

        Role: Renders interactive maps in the browser. Will display Nutzungszonen as vector data.

    Frontend Structure/Styling: Basic HTML, CSS (user's choice, e.g., Tailwind CSS, simple custom CSS).

Core Workflow & Data Flow:

    Initial Page Load:

        User accesses the main URL.

        FastAPI serves the initial HTML page. This page includes:

            Placeholders for the municipality list, map, and attribute table.

            Necessary JavaScript for initializing OpenLayers and any HTMX-triggered custom JavaScript.

            Links to OpenLayers CSS and JS libraries, and HTMX.

    Displaying Municipalities:

        HTMX on the frontend makes a request to a FastAPI endpoint (e.g., /api/municipalities).

        FastAPI queries DuckDB for the list of municipalities.

        FastAPI returns the list (e.g., as HTML fragments or JSON for HTMX to process into HTML).

        HTMX updates the designated section of the page to display the list.

    User Selects a Municipality:

        User clicks on a municipality name.

        This action (potentially managed by HTMX or a small JavaScript listener tied to HTMX-generated elements) triggers:

            A request to a FastAPI endpoint for Nutzungszonen data of the selected municipality (e.g., /api/municipalities/{id}/nutzungszonen_geojson).

            (Optionally) A separate request for tabular attribute data.

    Fetching and Displaying Map Data (Nutzungszonen):

        FastAPI receives the request for Nutzungszonen.

        FastAPI queries DuckDB using the spatial extension to retrieve Nutzungszonen geometries and relevant properties for the selected municipality. Crucially, DuckDB should output these geometries as GeoJSON.

        FastAPI returns a GeoJSON FeatureCollection to the frontend.

        Client-side JavaScript (triggered by HTMX success or directly) takes this GeoJSON data and uses OpenLayers API methods to:

            Clear any existing Nutzungszonen layers/sources.

            Add the new GeoJSON as a source to the OpenLayers map.

            Add a layer (or multiple layers) to visualize this source (e.g., vector layer for polygons).

            Style the layer (colors, opacity, borders based on Nutzungszonen attributes if desired).

            Optionally, fit the map view to the bounds of the loaded Nutzungszonen.

    Displaying Tabular Data:

        If a separate request for attribute data was made, HTMX fetches this from a FastAPI endpoint.

        FastAPI queries DuckDB for attributes.

        FastAPI returns data (e.g., as an HTML table fragment).

        HTMX swaps this fragment into the designated table placeholder.

    Map Interaction (Optional POC Extension):

        User clicks on a Nutzungszone feature on the MapLibre map.

        MapLibre GL JS event listener captures this click.

        JavaScript extracts properties of the clicked feature.

        This information can be used to:

            Highlight the selected feature on the map.

            Trigger an HTMX request to fetch more detailed attributes for that specific zone and display them in the table.

Responsibilities & Key Instructions for AI Development:

    FastAPI:

        Define Pydantic models for request/response validation if complex data structures are passed (though for GeoJSON, direct construction might be fine for a POC).

        Create clear API endpoints:

            GET /: Serves the main HTML page.

            GET /api/municipalities: Returns a list of municipalities.

            GET /api/municipalities/{municipality_id}/nutzungszonen_geojson: Returns GeoJSON for Nutzungszonen.

            GET /api/municipalities/{municipality_id}/attributes_table: (Optional) Returns HTML table fragment for attributes.

        Implement DuckDB connection and querying logic within these endpoints. Ensure queries use ST_AsGeoJSON() for geometry output.

    DuckDB:

        Schema design for municipalities and Nutzungszonen tables. Ensure spatial indexes if performance becomes a concern (may be overkill for POC).

        Queries must be efficient and select necessary attributes alongside geometries.

    HTML Structure:

        Clear div elements with IDs for:

            Municipality list container.

            MapLibre GL JS map container (e.g., <div id="map"></div>).

            Attribute table container.

    HTMX:

        Use hx-get, hx-trigger, hx-target, hx-swap attributes appropriately.

        Consider how HTMX will interact with JavaScript functions needed for MapLibre (e.g., HTMX can trigger a custom event that a JS listener picks up, or call a global JS function upon successful AJAX request using HX-Trigger response header or htmx.onLoad).

    OpenLayers Integration:

        Initialize the OpenLayers map with base tile layers (e.g., OSM, Swiss official maps).

        JavaScript functions will be needed to:

            Add/update a GeoJSON source to the map dynamically.

            Add/update vector layer(s) that reference this source and define their visual appearance (fill colors, stroke colors, etc.).

            Handle map events like clicks on features.

            Example pattern for updating data:

            javascript
            // Assume 'map' is your OpenLayers map instance
            // Assume 'geojsonFeatureCollection' is the data fetched from FastAPI

            // If the source already exists, update it
            if (map.getSource('nutzungszonen-source')) {
                map.getSource('nutzungszonen-source').setData(geojsonFeatureCollection);
            } else {
            // Otherwise, add a new source and layer
                map.addSource('nutzungszonen-source', {
                    type: 'geojson',
                    data: geojsonFeatureCollection
                });
                map.addLayer({
                    id: 'nutzungszonen-layer',
                    type: 'fill', // or 'line', 'symbol' etc.
                    source: 'nutzungszonen-source',
                    paint: {
                        'fill-color': '#007cbf', // Example color
                        'fill-opacity': 0.5
                    }
                });
            }

    Styling: Basic CSS to make the layout functional and clean for the POC.

Important Considerations for POC:

    Keep it Simple: Focus on the core workflow: select municipality -> display on map -> display basic attributes.

    GeoJSON is Key: Standardize on GeoJSON for transferring vector data from backend to OpenLayers.

    Error Handling: Basic error messages if data can't be fetched or displayed.

    OpenLayers Styles: Choose simple base map layers. Custom styling of Nutzungszonen should be functional rather than overly complex for the POC.







```markdown
# Geospatial Web Application Development Guide

## Project Overview
Build a geospatial web application that processes local GeoParquet and GPKG files through DuckDB, with interactive map visualization and multi-format data display.

## Core Architecture

### Backend (Python + DuckDB)
- **Framework**: FastAPI for REST API
- **Database**: DuckDB with spatial extension for geospatial queries
- **Data Sources**: Local GeoParquet and GPKG files
- **Query Processing**: Convert user inputs to SQL queries against DuckDB

### Frontend (OpenLayers)
- **Map Rendering**: OpenLayers for interactive maps
- **Tables**: Table components for tabular data display
- **User Interface**: Input forms/filters that generate SQL queries

## Implementation Steps

### 1. Backend Setup
```
# FastAPI example with DuckDB
from fastapi import FastAPI
import duckdb

app = FastAPI()
conn = duckdb.connect()
conn.execute("INSTALL spatial; LOAD spatial;")

@app.post("/query")
async def execute_query(query_params: dict):
    # Convert user inputs to SQL
    sql = build_spatial_query(query_params)
    results = conn.execute(sql).fetchdf()
    return format_geojson_response(results)


### 2. Spatial Query Patterns

-- Example spatial queries for DuckDB
SELECT name, ST_AsGeoJSON(geometry) AS geojson, properties
FROM read_parquet('data/points.parquet')
WHERE ST_Within(geometry, ST_GeomFromText('POLYGON(...)'));

-- GPKG file querying
SELECT * FROM ST_Read('data/boundaries.gpkg')
WHERE attribute_column = 'filter_value';


### 3. Frontend Integration

// Map visualization with query results
const updateMap = async (filters) => {
  const response = await fetch('/api/query', {
    method: 'POST',
    body: JSON.stringify(filters)
  });

  const geoJSON = await response.json();
  map.getSource('results').setData(geoJSON);
};

// Chart generation from query results
const createChart = (data) => {
  new Chart(ctx, {
    type: 'bar',
    data: {
      labels: data.map(d => d.name),
      datasets: [{data: data.map(d => d.value)}]
    }
  });
};


## Key Dependencies

### Python Backend

fastapi
uvicorn
duckdb


### Frontend

{
  "dependencies": {
    "ol": "^10.5.0",
    "proj4": "^2.11.0"
  }
}


## Performance Optimization
- Use spatial indexing for faster queries
- Implement query result caching
- Leverage DuckDB's columnar storage advantages
- Consider data partitioning for large datasets

## Future Cloud Migration
- Replace local files with S3/cloud storage
- Use DuckDB's HTTPFS extension for remote Parquet access
- Deploy backend as serverless functions
- Host frontend on CDN (Vercel/Netlify)

## Development Workflow
1. Start with local file exploration using DuckDB CLI
2. Build backend API endpoints incrementally
3. Create frontend components for user interaction
4. Integrate map visualization with query results
5. Add charting and table display capabilities
6. Optimize performance and add error handling


---




# How-To: Geospatial API with FastAPI & DuckDB

## 1. Project Goal
Develop a FastAPI application to serve geospatial data from local GeoParquet and GPKG files. The API will accept query parameters, translate them into SQL queries executed by DuckDB (with its spatial extension), and return results suitable for map display and other visualizations.

## 3. Setup & Dependencies

**`requirements.txt`**:
fastapi
duckdb


*Note on Concurrency*: For a production FastAPI app with many concurrent users, managing DuckDB connections requires care. DuckDB allows multiple threads from the *same* Python process to use the *same* connection. For multiple *processes* (like with Gunicorn workers), each process needs its own connection, typically to a file-based database. In-memory databases are private to the connection that created them.



## 6. Using DuckDB Features

*   **Direct File Reading**: `read_parquet('path/to/file.geoparquet')` and `ST_Read('path/to/file.gpkg')` are used directly in queries.
*   **Spatial Functions**: `ST_Intersects`, `ST_MakeEnvelope`, `ST_AsGeoJSON` are core to the spatial querying.
*   **Named Parameters**: `con.execute(sql, {"param_name": value})` is used for safer and cleaner queries [3].
*   **Fetching DataFrames**: `fetchdf()` is used to get results as Pandas DataFrames, which are easy to process.
*   **Macros**: The `GetPointsInBounds` macro demonstrates encapsulating reusable SQL logic within DuckDB itself.
*   **HTTPFS Extension**: Loaded for potential future use with remote Parquet/CSV files (e.g., from S3).
*   **Configuration via `PRAGMA`s or `SET`**: While not explicitly shown in API logic, you can set DuckDB configurations (e.g., `SET memory_limit='1GB';`) via `con.execute()`.

## 7. Further Enhancements
*   **Advanced Filtering**: More sophisticated filter construction in `geospatial_service.py`.
*   **Dynamic Table/File Registration**: Scan the `DATA_DIR_PATH` and register views in DuckDB at startup.
*   **Connection Management**: Implement proper connection pooling or per-request connections for high-concurrency production environments.
*   **Error Handling**: More granular error handling.
*   **Caching**: Implement caching for frequently requested queries/results.
*   **Asynchronous Database Calls**: For truly non-blocking DB operations with FastAPI, consider libraries that allow running synchronous DuckDB calls in a thread pool (FastAPI does this by default for `def` route handlers, but explicit management might be needed for complex scenarios or other async ORMs/drivers if DuckDB had one).
*   **`duckdb-fastapi` package**: Explore `duckdb-fastapi` [2] for automatically generating API endpoints from DuckDB macros, which could simplify some routing if you heavily rely on macros.

This guide provides a solid foundation. Adapt the file paths, column names (`geom`), and query logic to match your specific datasets.





